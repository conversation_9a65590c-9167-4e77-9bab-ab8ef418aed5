package com.wnkx.order.remote;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.domain.vo.TagVO;
import com.ruoyi.common.core.domain.vo.UserVO;
import com.ruoyi.common.core.enums.LogisticMainStatus;
import com.ruoyi.common.core.enums.StatusTypeEnum;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.*;
import com.ruoyi.system.api.domain.dto.BusinessAccountDetailDTO;
import com.ruoyi.system.api.domain.dto.ModelListDTO;
import com.ruoyi.system.api.domain.dto.biz.business.BusinessAccountDTO;
import com.ruoyi.system.api.domain.dto.biz.business.BusinessDTO;
import com.ruoyi.system.api.domain.dto.biz.business.account.user.BizUserDetailListDTO;
import com.ruoyi.system.api.domain.dto.biz.business.balance.*;
import com.ruoyi.system.api.domain.dto.biz.channel.DistributionChannelOrderBatchDTO;
import com.ruoyi.system.api.domain.dto.biz.channel.DistributionChannelOrderDTO;
import com.ruoyi.system.api.domain.dto.biz.logistic.LogisticListDTO;
import com.ruoyi.system.api.domain.dto.biz.model.CannotAcceptModelDTO;
import com.ruoyi.system.api.domain.dto.biz.translate.TranslateBatchDTO;
import com.ruoyi.system.api.domain.dto.order.AsyncCrawlTask;
import com.ruoyi.system.api.domain.dto.order.OrderMemberChannelListDTO;
import com.ruoyi.system.api.domain.dto.order.WithdrawDepositRecordDTO;
import com.ruoyi.system.api.domain.dto.system.SysUserListDTO;
import com.ruoyi.system.api.domain.entity.Model;
import com.ruoyi.system.api.domain.entity.ModelPerson;
import com.ruoyi.system.api.domain.entity.SysDictData;
import com.ruoyi.system.api.domain.entity.SysUser;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessBalancePrepay;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessMemberActivity;
import com.ruoyi.system.api.domain.entity.biz.channel.DistributionChannel;
import com.ruoyi.system.api.domain.entity.biz.model.UserModelBlacklist;
import com.ruoyi.system.api.domain.entity.order.datastatistics.CustomerServiceAddedOustModelCountInfo;
import com.ruoyi.system.api.domain.vo.LogisticInfoVO;
import com.ruoyi.system.api.domain.vo.LogisticVO;
import com.ruoyi.system.api.domain.vo.SysUserVO;
import com.ruoyi.system.api.domain.vo.TagListVO;
import com.ruoyi.system.api.domain.vo.biz.business.*;
import com.ruoyi.system.api.domain.vo.biz.business.balance.BusinessBalanceDetailLockInfoVO;
import com.ruoyi.system.api.domain.vo.biz.business.balance.WithdrawDepositRecordVO;
import com.ruoyi.system.api.domain.vo.biz.business.user.BizUserDetailVO;
import com.ruoyi.system.api.domain.vo.biz.business.user.UserBlackModelVO;
import com.ruoyi.system.api.domain.vo.biz.channel.ChannelBrokeRageVO;
import com.ruoyi.system.api.domain.vo.biz.model.AddPreselectModelListVO;
import com.ruoyi.system.api.domain.vo.biz.model.ModelChangeVO;
import com.ruoyi.system.api.domain.vo.biz.model.ModelInfoVO;
import com.ruoyi.system.api.domain.vo.biz.model.ModelOrderSimpleVO;
import com.ruoyi.system.api.domain.vo.order.OrderMemberChannelListVO;
import com.ruoyi.system.api.domain.vo.order.WorkbenchVO;
import com.ruoyi.system.api.domain.vo.order.datastatistics.ChineseCustomerServiceDataVO;
import com.ruoyi.system.api.domain.vo.order.datastatistics.EnglishCustomerServiceDataVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/6/18 9:36
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class RemoteService {

    /**
     * 商家服务api
     */
    private final RemoteBusinessAccountService remoteBusinessAccountService;
    /**
     * 物流服务api
     */
    private final RemoteLogisticService remoteLogisticService;
    /**
     * 模特服务api
     */
    private final RemoteModelService remoteModelService;
    /**
     * 用户服务api
     */
    private final RemoteUserService remoteUserService;

    /**
     * 标签服务api
     */
    private final RemoteTagService remoteTagService;

    /**
     * 文件服务api
     */
    private final RemoteAmazonService remoteAmazonService;

    private final RemoteConfigService remoteConfigService;
    private final RemoteDistributionChannelService remoteDistributionChannelService;
    private final RemoteTranslateService remoteTranslateService;
    private final RemoteDictDataService remoteDictDataService;
    private final RemoteUserModelBlacklistService remoteUserModelBlacklistService;

    /**
     * 商家排单时 更新最近排单时间 以及删除未排单事件
     */
    public void updateRecentOrderTime(Long businessId) {
        Assert.isTrue(remoteBusinessAccountService.updateRecentOrderTime(businessId, SecurityConstants.INNER), "更新商家回访时间失败");
    }

    public void updatePaySucceed(Long businessId) {
        Assert.isTrue(remoteBusinessAccountService.updatePaySucceed(businessId, SecurityConstants.INNER), "更新商家回访时间失败");
    }

    /**
     * 获取充值数据详情
     *
     * @param orderNum
     * @return
     */
    public BusinessBalancePrepay getInnerOnlineDetail(String orderNum) {
        return remoteBusinessAccountService.innerOnlineDetail(orderNum, SecurityConstants.INNER);
    }

    /**
     * 提交凭证
     *
     * @param dto
     */
    public void innerSubmitCredential(OnlineRechargeSubmitCredentialDTO dto) {
        Assert.notNull(remoteBusinessAccountService.innerSubmitCredential(dto, SecurityConstants.INNER), "提交凭证失败~");
    }

    /**
     * 重置数据
     *
     * @param orderNums
     */
    public void innerUpdateBatchFieldNullToNull(Collection<String> orderNums) {
        Assert.notNull(remoteBusinessAccountService.updateBatchFieldNullToNull(orderNums, SecurityConstants.INNER), "重置数据失败~");
    }

    /**
     * 获取当前用户拉黑模特列表
     */
    public List<UserModelBlacklist> selectBlackModelListByBizUserId() {
        return remoteUserModelBlacklistService.selectBlackModelListByBizUserId(SecurityConstants.INNER);
    }


    /**
     * 获取用户拉黑模特列表
     */
    public List<UserBlackModelVO> userBlackModelListByBizUserId(Long bizUserId) {
        return remoteUserModelBlacklistService.userBlackModelListByBizUserId(bizUserId, SecurityConstants.INNER);
    }
//
//    /**
//     * 根据种草码 获取分销渠道信息
//     */
//    @Deprecated(since = "于5/12~5/16周开发计划 更新", forRemoval = true)
//    public BigDecimal getDistributionChannelBySeedCode(String seedCode) {
//        return remoteDistributionChannelService.getDistributionChannelBySeedCode(seedCode, SecurityConstants.INNER);
//    }

    /**
     * 根据种草码 获取分销渠道信息
     */
    public ChannelBrokeRageVO getDistributionChannelBySeedCodeV1(String seedCode) {
        return remoteDistributionChannelService.getFissionBrokeRageBySeedCode(seedCode, SecurityConstants.INNER);
    }

    public DistributionChannel getDistributionChannelEntityBySeedCode(String seedCode) {
        return remoteDistributionChannelService.getDistributionChannelEntityBySeedCode(seedCode, SecurityConstants.INNER);
    }

    public List<DistributionChannel> queryDistributionChannelsBySeedCodes(Collection<String> seedCodes) {
        if (CollUtil.isEmpty(seedCodes)) {
            return Collections.emptyList();
        }
        return remoteDistributionChannelService.queryDistributionChannelsBySeedCodes(seedCodes, SecurityConstants.INNER);

    }

    /**
     * 保存渠道订单数据
     *
     * @param dto
     */
    public void saveDistributionChannelOrder(DistributionChannelOrderDTO dto) {
        remoteDistributionChannelService.saveDistributionChannelOrder(dto, SecurityConstants.INNER);
    }

    public List<OrderMemberChannelListVO> innerMemberChannelListByCondition(OrderMemberChannelListDTO dto) {
        return remoteDistributionChannelService.innerMemberChannelListByCondition(dto, SecurityConstants.INNER);
    }

    /**
     * 批量保存渠道订单数据
     *
     * @param dto
     */
    public void saveBatchDistributionChannelOrder(DistributionChannelOrderBatchDTO dto) {
        remoteDistributionChannelService.saveBatchDistributionChannelOrder(dto, SecurityConstants.INNER);
    }

    /**
     * 获取标签信息
     *
     * @param tagIds 标签id
     */
    public List<TagVO> getTagVO(Collection<Long> tagIds) {
        if (CollUtil.isEmpty(tagIds)) {
            return new ArrayList<>();
        }

        List<TagListVO> dataList = remoteTagService.queryList(tagIds, SecurityConstants.INNER);

        List<TagVO> tagVOList = new ArrayList<>();
        for (TagListVO tagListVO : dataList) {
            TagVO tagVO = new TagVO();
            tagVO.setId(tagListVO.getId());
            tagVO.setName(tagListVO.getName());
            tagVOList.add(tagVO);
        }
        return tagVOList;
    }

    /**
     * 查询模特关联运营
     *
     * @param modelId 模特id
     */
    public Map<Long, ModelPerson> getModelPersonMap(Collection<Long> modelId) {
        List<ModelPerson> modelPeople = remoteModelService.queryModelPerson(modelId, SecurityConstants.INNER);
        return modelPeople.stream().collect(Collectors.toMap(ModelPerson::getModelId, p -> p));
    }

    /**
     * 查询模特关联运营
     *
     * @param modelId 模特id
     */
    public List<ModelPerson> queryModelPerson(Collection<Long> modelId) {
        return remoteModelService.queryModelPerson(modelId, SecurityConstants.INNER);
    }

    /**
     * 查询当前登录运营关联模特
     */
    public List<ModelPerson> selectCurrentUserRelevanceModel() {
        return remoteModelService.selectModelPersonByUserIds(List.of(SecurityUtils.getUserId()), SecurityConstants.INNER);
    }

    /**
     * 查询运营关联模特
     */
    public List<ModelPerson> selectBackUserRelevanceModelByUserIds(Collection<Long> userIds) {
        return remoteModelService.selectModelPersonByUserIds(userIds, SecurityConstants.INNER);
    }

    public String deleteBusiness(Long businessId) {
        return remoteBusinessAccountService.deleteBusiness(businessId, SecurityConstants.INNER);
    }

    /**
     * 获取视频订单提现记录
     */
    public List<WithdrawDepositRecordVO> withdrawDepositRecord(WithdrawDepositRecordDTO dto) {
        return remoteBusinessAccountService.withdrawDepositRecord(dto, SecurityConstants.INNER);
    }

    /**
     * 查询模特信息
     */
    public Map<Long, ModelInfoVO> getModelMap(Collection<Long> modelId) {
        if (CollUtil.isEmpty(modelId)) {
            return Collections.emptyMap();
        }
        ModelListDTO modelListDTO = new ModelListDTO();
        modelListDTO.setId(modelId);

        List<ModelInfoVO> modelList = this.innerList(modelListDTO);
        if (CollUtil.isEmpty(modelList)) return new HashMap<>();
        Map<Long, ModelInfoVO> modelMap = new HashMap<>();

        for (ModelInfoVO model : modelList) {
            modelMap.put(model.getId(), model);
        }

        return modelMap;
    }

    /**
     * 查询模特信息
     */
    public Map<Long, ModelInfoVO> getModelMapWithBlackList(Collection<Long> modelId) {
        if (CollUtil.isEmpty(modelId)) {
            return Collections.emptyMap();
        }
        ModelListDTO modelListDTO = new ModelListDTO();
        modelListDTO.setId(modelId);
        modelListDTO.setNeedBlacklist(StatusTypeEnum.YES.getCode());
        List<ModelInfoVO> modelList = this.innerList(modelListDTO);
        if (CollUtil.isEmpty(modelList)) return new HashMap<>();
        Map<Long, ModelInfoVO> modelMap = new HashMap<>();

        for (ModelInfoVO model : modelList) {
            modelMap.put(model.getId(), model);
        }

        return modelMap;
    }

    /**
     * 查询模特信息
     */
    public List<ModelChangeVO> selectModelChangeList(Collection<Long> modelId) {
        if (CollUtil.isEmpty(modelId)) {
            return Collections.emptyList();
        }
        return remoteModelService.selectModelChangeList(ModelListDTO.builder().id(modelId).build(), SecurityConstants.INNER);
    }

    /**
     * 获取模特简单信息map
     */
    public Map<Long, ModelOrderSimpleVO> getModelSimpleMap(Collection<Long> modelId) {
        if (CollUtil.isEmpty(modelId)) {
            return Collections.emptyMap();
        }
        ModelListDTO modelListDTO = new ModelListDTO();
        modelListDTO.setId(modelId);

        List<ModelOrderSimpleVO> modelList = queryModelSimpleList(modelListDTO);
        if (CollUtil.isEmpty(modelList)) {
            return Collections.emptyMap();
        }

        return modelList.stream().collect(Collectors.toMap(ModelOrderSimpleVO::getId, p -> p));
    }

    /**
     * 客服数据-中文部客服数据
     */
    public List<ChineseCustomerServiceDataVO> selectChineseCustomerServiceData() {
        List<ChineseCustomerServiceDataVO> chineseCustomerServiceDataVOS = remoteUserService.selectChineseCustomerServiceData(SecurityConstants.INNER);
        Assert.notNull(chineseCustomerServiceDataVOS, "调用远程服务[获取中文部客服数据]失败");
        return chineseCustomerServiceDataVOS;
    }

    /**
     * 客服数据-英文部客服数据
     */
    public List<EnglishCustomerServiceDataVO> selectEnglishCustomerServiceData() {
        List<EnglishCustomerServiceDataVO> englishCustomerServiceDataVOS = remoteUserService.selectEnglishCustomerServiceData(SecurityConstants.INNER);
        Assert.notNull(englishCustomerServiceDataVOS, "调用远程服务[获取中文部客服数据]失败");
        return englishCustomerServiceDataVOS;
    }

    /**
     * 获取用户信息列表
     */
    public List<SysUser> getUserList(SysUserListDTO dto) {
        return remoteUserService.listNoPage(dto, SecurityConstants.INNER);
    }

    /**
     * 根据用户ID查询用户完整信息
     * 用于获取最新的用户权限信息，解决缓存数据不实时的问题
     *
     * @param userId 用户ID
     * @return 用户完整信息，包含selection_management等所有字段
     */
    public SysUser selectUserById(Long userId) {
        return remoteUserService.selectUserById(userId, SecurityConstants.INNER);
    }

    /**
     * 获取用户简单信息列表
     */
    public List<UserVO> getUserList(Collection<Long> userIdList) {
        if (CollUtil.isEmpty(userIdList)) {
            return Collections.emptyList();
        }
        SysUserListDTO dto = new SysUserListDTO();
        dto.setUserId(userIdList);
        List<SysUser> userList = getUserList(dto);
        List<UserVO> userVOS = new ArrayList<>();
        userList.forEach(user -> {
            UserVO userVO = new UserVO();
            userVO.setId(user.getUserId());
            userVO.setName(user.getUserName());
            userVOS.add(userVO);

        });
        return userVOS;
    }

    /**
     * 获取用户信息map
     */
    public Map<Long, UserVO> getUserMap(SysUserListDTO dto) {
        List<SysUser> users = getUserList(dto);

        Map<Long, UserVO> userMap = new HashMap<>();

        if (CollUtil.isEmpty(users)) {
            return userMap;
        }

        for (SysUser sendUser : users) {
            UserVO userVO = new UserVO();
            userVO.setId(sendUser.getUserId());
            userVO.setName(sendUser.getUserName());
            userVO.setPhonenumber(sendUser.getPhonenumber());

            userMap.put(userVO.getId(), userVO);
        }
        return userMap;
    }

    /**
     * 获取物流信息map
     *
     * @param numbers 物流单号
     */
    public Map<String, LogisticVO> getLogisticMap(Collection<String> numbers) {
        if (CollUtil.isEmpty(numbers)) {
            return new HashMap<>();
        }

        List<LogisticVO> data = remoteLogisticService.selectListByNumbers(numbers, SecurityConstants.INNER);

        Map<String, LogisticVO> map = new HashMap<>();

        if (CollUtil.isEmpty(data)) {
            return map;
        }

        for (LogisticVO logistic : data) {
            map.put(logistic.getNumber(), logistic);
        }
        return map;
    }

    /**
     * 通过条件获取物流单号
     */
    public Collection<String> getLogisticNumbersByCondition(Collection<String> numbers, Collection<Integer> mainStatus) {
        List<String> mainStatusLabel = new ArrayList<>();

        for (Integer status : mainStatus) {
            mainStatusLabel.add(LogisticMainStatus.getLabelByCode(status));
        }
        return remoteLogisticService.getNumbersByCondition(LogisticListDTO.builder().numbers(numbers).mainStatus(mainStatusLabel).build(), SecurityConstants.INNER);
    }

    /**
     * 通过物流单号获取最新的物流信息
     */
    public Map<String, LogisticInfoVO> getLastLogisticInfoMap(Collection<String> numbers) {
        List<LogisticInfoVO> list = remoteLogisticService.getLastLogisticInfo(numbers, SecurityConstants.INNER);
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyMap();
        }
        return list.stream().collect(Collectors.toMap(LogisticInfoVO::getNumber, p -> p));
    }

    public BusinessVO getBusinessVo(BusinessDTO dto) {
        BusinessVO businessVo = remoteBusinessAccountService.getBusinessVo(dto, SecurityConstants.INNER);
        Assert.notNull(businessVo, "获取商家信息失败");
        return businessVo;
    }

    public WorkbenchVO getFinanceWorkbenchVo() {
        return remoteBusinessAccountService.getFinanceWorkbenchVo(SecurityConstants.INNER);
    }

    /**
     * 获取有效提现锁定数据
     *
     * @param dto
     * @return
     */
    public List<BusinessBalanceDetailLockInfoVO> queryValidLockList(BusinessBalanceDetailLockInfoDTO dto) {
        return remoteBusinessAccountService.queryValidLockList(dto, SecurityConstants.INNER);
    }

    /**
     * 注册物流单号
     */
    public void register(Collection<String> numbers) {
        Assert.isTrue(remoteLogisticService.register(numbers, SecurityConstants.INNER), "注册物流单号失败");
    }

    /**
     * 查询商家信息
     */
    public List<BusinessAccountDetailVO> queryMerchant(BusinessAccountDetailDTO dto) {
        return remoteBusinessAccountService.getBusinessAccountDetailVOs(dto);
    }

    /**
     * 查询用户信息
     *
     * @param dto
     * @return
     */
    public List<BusinessAccountDetailVO> queryUserInfo(BusinessAccountDetailDTO dto) {
        return remoteBusinessAccountService.queryUserInfo(dto);
    }

    /**
     * 查询商家信息
     */
    public List<BusinessAccountDetailVO> queryMerchantBySearchName(String searchName) {
        if (StrUtil.isBlank(searchName)) {
            return new ArrayList<>();

        }
        return remoteBusinessAccountService.getBusinessAccountDetailVOs(BusinessAccountDetailDTO.builder().searchNameMemberCodeAccount(searchName).build());
    }

    /**
     * 查询登录账号
     */
    public List<BizUserDetailVO> getBizUserDetailList(BizUserDetailListDTO dto) {
        return remoteBusinessAccountService.getBizUserDetailList(dto, SecurityConstants.INNER);
    }

    /**
     * 查询用户渠道信息
     */
    public List<BizUserDetailVO> getUserChannel(BizUserDetailListDTO dto) {
        return remoteBusinessAccountService.getUserChannel(dto, SecurityConstants.INNER);
    }

    /**
     * 获取预付列表
     *
     * @param dto
     * @return
     */
    public List<BusinessBalancePrepayVO> innerBusinessBalancePrepayList(BusinessBalancePrepayListDTO dto) {
        return remoteBusinessAccountService.innerBusinessBalancePrepayList(dto, SecurityConstants.INNER);
    }

    /**
     * 修改钱包充值appid
     *
     * @param dto
     * @return
     */
    public void innerBusinessBalancePrepayUpdateAppId(PrepayUpdateAppIdDTO dto) {
        Assert.notNull(remoteBusinessAccountService.innerBusinessBalancePrepayUpdateAppId(dto, SecurityConstants.INNER), "修改钱包充值appid失败");
    }

    /**
     * 修改钱包充值状态
     *
     * @param dto
     */
    public BusinessBalancePrepay innerUpdateOrderPayStatus(PrepayUpdatePayStatusDTO dto) {
        BusinessBalancePrepay businessBalancePrepay = remoteBusinessAccountService.innerUpdateOrderPayStatus(dto, SecurityConstants.INNER);
        Assert.notNull(businessBalancePrepay, "修改钱包充值appid失败");

        return businessBalancePrepay;
    }

    /**
     * 获取预付款审核统计*
     *
     * @return
     */
    public BusinessBalancePrepayStatisticsVO getInnerStatistics() {
        return remoteBusinessAccountService.getInnerStatistics(SecurityConstants.INNER);
    }

    /**
     * 查询商家信息
     */
    public List<BusinessAccountDetailVO> queryMerchantByBusinessIdsAndAccountIds(Collection<Long> businessIds, Collection<Long> accountIds) {
        return queryMerchant(BusinessAccountDetailDTO.builder().ids(accountIds).businessIds(businessIds).build());
    }

    /**
     * 查询商家信息
     */
    public BusinessAccountVO getBusinessAccountByAccountId(Long accountId) {
        BusinessAccountDTO dto = new BusinessAccountDTO();
        dto.setId(accountId);
        return remoteBusinessAccountService.getBusinessAccountOne(dto, SecurityConstants.INNER);
    }

    public Map<String,Integer> getUserMemberStatusBySeedId(Collection<String> seedId) {
        List<BusinessAccountDetailVO> businessAccountData =  remoteBusinessAccountService.getUserMemberStatusBySeedId(SecurityConstants.INNER, seedId);

        if (CollUtil.isNotEmpty(businessAccountData)) {
            return businessAccountData.stream()
                    .filter(item -> StrUtil.isNotBlank(item.getSeedId()))
                    .collect(Collectors.toMap(
                            BusinessAccountDetailVO::getSeedId,
                            BusinessAccountDetailVO::getMemberStatus,
                            (existing, replacement) -> existing
                    ));
        }
        return new HashMap<>();
    }

    /**
     * 查询商家信息（携带活动）
     */
    public BusinessAccountVO getBusinessAccountWithActivityByAccountId(Long accountId, Integer packageType) {
        BusinessAccountDTO dto = new BusinessAccountDTO();
        dto.setId(accountId);
        dto.setNeedActivity(StatusTypeEnum.YES.getCode());
        dto.setPackageType(packageType);
        return remoteBusinessAccountService.getBusinessAccountOne(dto, SecurityConstants.INNER);
    }

    /**
     * 获取商家活动
     *
     * @param packageType
     * @return
     */
    public BusinessMemberActivity getBusinessMemberActivity(Integer packageType) {
        return remoteBusinessAccountService.getBusinessMemberActivity(packageType, SecurityConstants.INNER);
    }

    /**
     * 通过unionId查询商家信息
     */
    public BusinessAccountVO getBusinessAccountByUnionId(String unionId) {
        return remoteBusinessAccountService.getAccountInfo(unionId, SecurityConstants.INNER);
    }

    public void refreshToken() {
        remoteBusinessAccountService.refreshToken(SecurityConstants.INNER);
    }


    /**
     * 查询不可接单的模特（内部请求）
     *
     * @return 不可接单的模特
     */
    public List<Model> queryCannotAcceptList(CannotAcceptModelDTO dto) {
        return remoteModelService.queryCannotAcceptList(dto, SecurityConstants.INNER);
    }

    /**
     * 查询模特信息列表（内部请求）
     */
    public List<ModelInfoVO> innerList(ModelListDTO modelListDTO) {
        return remoteModelService.innerList(modelListDTO, SecurityConstants.INNER);
    }

    /**
     * 查询模特简单信息（用于订单列表模特数据）
     */
    public List<ModelOrderSimpleVO> queryModelSimpleList(ModelListDTO modelListDTO) {
        return remoteModelService.queryModelSimpleList(modelListDTO, SecurityConstants.INNER);
    }

    /**
     * 查询模特信息（预选模特对象）
     */
    public List<AddPreselectModelListVO> selectModelInfoOfPreselection(ModelListDTO modelListDTO) {
        return remoteModelService.selectModelInfoOfPreselection(modelListDTO, SecurityConstants.INNER);
    }

    /**
     * 模糊查询模特信息列表（模特名称、模特账号）
     */
    public List<ModelInfoVO> queryLikeModelList(ModelListDTO modelListDTO) {
        return remoteModelService.queryLikeModelList(modelListDTO, SecurityConstants.INNER);
    }

    /**
     * 异步批量抓取amazon产品链接图片并更新视频订单
     */
    public void asyncUpdateOrderVideoImage(AsyncCrawlTask asyncCrawlTask) {
        remoteAmazonService.asyncUpdateOrderVideoImage(asyncCrawlTask, SecurityConstants.INNER);
    }

    /**
     * 批量翻译
     */
    public List<String> translateBatch(TranslateBatchDTO translateBatchDTO) {
        return remoteTranslateService.translateBatch(translateBatchDTO, SecurityConstants.INNER);
    }

    /**
     * 获取当前用户以及他下级部门的用户信息
     */
    public List<SysUserVO> getUserLevel(String source) {
        return remoteUserService.getUserLevel(source);
    }

    /**
     * 根据参数键名查询参数值
     *
     * @param configKey 参数键名
     * @return 结果
     */
    public String getConfigKey(String configKey) {
        return remoteConfigService.getConfigKey(configKey, SecurityConstants.INNER);
    }

    /**
     * 根据字典类型查询字典数据信息
     */
    public List<SysDictData> selectDictDataByType(String dictType) {
        return remoteDictDataService.selectDictDataByType(dictType, SecurityConstants.INNER);
    }

    public List<CustomerServiceAddedOustModelCountInfo> getEnglishCustomerServiceAddedOustModelCounts(String date) {
        List<CustomerServiceAddedOustModelCountInfo> englishCustomerServiceAddedOustModelCounts = remoteModelService.getEnglishCustomerServiceAddedOustModelCounts(date, SecurityConstants.INNER);
        Assert.notNull(englishCustomerServiceAddedOustModelCounts, "调用远程服务[获取英文部客服 新增/淘汰模特数]失败");
        return englishCustomerServiceAddedOustModelCounts;
    }

    /**
     * 查询需要清理的预选模特（状态异常和不展示的模特）
     * 用于预选模特清理功能，查询暂停合作、行程中、取消合作以及不展示的模特
     *
     * @return 需要清理的模特信息列表
     */
    public List<ModelInfoVO> getModelsForPreselectCleanup() {
        List<ModelInfoVO> invalidModels = remoteModelService.getModelsForPreselectCleanup(SecurityConstants.INNER);
        return invalidModels;
    }

    public static class RemoteUtil {
        /**
         * 获取商家信息map
         */
        public static Map<Long, BusinessAccountDetailVO> getMerchantMap(List<BusinessAccountDetailVO> businessAccounts) {
            Map<Long, BusinessAccountDetailVO> businessMap = new HashMap<>();

            if (CollUtil.isEmpty(businessAccounts)) {
                return businessMap;
            }

            for (BusinessAccountDetailVO business : businessAccounts) {
                businessMap.put(business.getBusinessId(), business);
            }

            return businessMap;
        }

        /**
         * 获取商家子账号信息map
         */
        public static Map<Long, BusinessAccountDetailVO> getAccountMap(List<BusinessAccountDetailVO> businessAccounts) {
            Map<Long, BusinessAccountDetailVO> accountMap = new HashMap<>();

            if (CollUtil.isEmpty(businessAccounts)) {
                return accountMap;
            }

            for (BusinessAccountDetailVO account : businessAccounts) {
                accountMap.put(account.getId(), account);
            }

            return accountMap;
        }
    }
}
