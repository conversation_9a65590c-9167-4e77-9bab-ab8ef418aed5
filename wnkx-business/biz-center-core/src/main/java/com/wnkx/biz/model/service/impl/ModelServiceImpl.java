package com.wnkx.biz.model.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageInfo;
import com.ruoyi.common.core.config.ModelProperties;
import com.ruoyi.common.core.constant.BusinessConstants;
import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.core.constant.OrderConstant;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.domain.dto.OrderByDto;
import com.ruoyi.common.core.domain.vo.UserVO;
import com.ruoyi.common.core.enums.*;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.*;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.RemoteSysDictTypeService;
import com.ruoyi.system.api.domain.dto.ModelListDTO;
import com.ruoyi.system.api.domain.dto.biz.business.BusinessAccountCollectModelDTO;
import com.ruoyi.system.api.domain.dto.biz.model.*;
import com.ruoyi.system.api.domain.dto.order.OutPreselectModelDTO;
import com.ruoyi.system.api.domain.dto.order.UpdateIssueIdDTO;
import com.ruoyi.system.api.domain.dto.order.logistic.ModelUpdateAddressDTO;
import com.ruoyi.system.api.domain.dto.system.SysUserListDTO;
import com.ruoyi.system.api.domain.entity.Model;
import com.ruoyi.system.api.domain.entity.ModelPerson;
import com.ruoyi.system.api.domain.entity.SysDictData;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessAccountCollectModel;
import com.ruoyi.system.api.domain.entity.biz.common.BizResource;
import com.ruoyi.system.api.domain.entity.biz.model.*;
import com.ruoyi.system.api.domain.entity.order.datastatistics.CustomerServiceAddedOustModelCountInfo;
import com.ruoyi.system.api.domain.vo.LoginUserInfoVO;
import com.ruoyi.system.api.domain.vo.biz.business.user.ModelBlackListUserVO;
import com.ruoyi.system.api.domain.vo.biz.business.user.UserBlackModelVO;
import com.ruoyi.system.api.domain.vo.biz.model.*;
import com.ruoyi.system.api.domain.vo.order.ModelOrderVO;
import com.ruoyi.system.api.domain.vo.order.datastatistics.EnglishCustomerServiceDataVO;
import com.wnkx.biz.business.service.IBusinessAccountCollectModelService;
import com.wnkx.biz.business.service.IUserModelBlacklistService;
import com.wnkx.biz.model.mapper.ModelMapper;
import com.wnkx.biz.model.service.*;
import com.wnkx.biz.remote.RemoteService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 模特信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-20
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ModelServiceImpl extends ServiceImpl<ModelMapper, Model> implements IModelService {
    /**
     * 模特案例视频资源服务
     */
    private final IModelVideoResourceService modelResourceService;
    /**
     * 模特标签服务
     */
    private final IModelTagService modelTagService;
    /**
     * 模特行程时间服务
     */

    private final IModelTravelService modelTravelService;
    /**
     * 模特关联人员服务
     */
    private final IModelPersonService modelPersonService;
    private final RemoteService remoteService;
    private final IModelAccountService modelAccountService;


    private final ThreadPoolTaskExecutor asyncPoolTaskExecutor;
    private final BizResourceService bizResourceService;

    private final RedisService redisService;

    private final ModelChangeRecordService modelChangeRecordService;

    private final IUserModelBlacklistService userModelBlacklistService;

    private final ModelProperties modelProperties;
    private String ERROR_REPORTED_ASSEMBLING_MODEL_LIST = "组装模特列表报错";

    private Integer modelTopMax = 55;

    private final RemoteSysDictTypeService remoteSysDictTypeService;
    private final ModelAddedSnapShootService modelAddedSnapShootService;
    private final ModelOustSnapShootService modelOustSnapShootService;

    /**
     * 客服数据-获取英文部关联模特数据
     */
    @Override
    public List<EnglishCustomerServiceDataVO> selectEnglishCustomerServiceModelData() {
        return baseMapper.selectEnglishCustomerServiceModelData();
    }

    /**
     * 查询需要清理的预选模特（状态异常和不展示的模特）
     * 包括：暂停合作(1)、行程中(2)、取消合作(3)的模特，以及不展示(is_show=0)的模特
     *
     * @return 需要清理的模特信息列表
     */
    @Override
    public List<ModelInfoVO> getModelsForPreselectCleanup() {
        try {
            log.info("开始查询需要清理的预选模特");

            // 构建查询条件：状态异常的模特（暂停合作、行程中、取消合作）
            List<Integer> invalidStatuses = Arrays.asList(
                ModelStatusEnum.PAUSE.getCode(),    // 暂停合作
                ModelStatusEnum.JOURNEY.getCode(),  // 行程中
                ModelStatusEnum.CANCEL.getCode()    // 取消合作
            );

            // 查询状态异常或不展示的模特
            List<ModelInfoVO> invalidModels = baseMapper.selectModelsForPreselectCleanup(invalidStatuses);

            log.info("查询到需要清理的预选模特数量: {}", invalidModels.size());
            return invalidModels;

        } catch (Exception e) {
            log.error("查询需要清理的预选模特时发生异常", e);
            return Collections.emptyList();
        }
    }

    /**
     * 获取英文部客服 新增/淘汰模特数
     */
    @Override
    public List<CustomerServiceAddedOustModelCountInfo> getEnglishCustomerServiceAddedOustModelCounts(String date) {
        List<CustomerServiceAddedOustModelCountInfo> englishCustomerServiceAddedModelCounts = modelPersonService.getEnglishCustomerServiceAddedModelCounts(date);
        List<CustomerServiceAddedOustModelCountInfo> englishCustomerServiceOustModelCounts = modelChangeRecordService.getEnglishCustomerServiceOustModelCounts(date);

        Map<Long, CustomerServiceAddedOustModelCountInfo> resultMap = new HashMap<>();

        // 合并 addedCount
        for (CustomerServiceAddedOustModelCountInfo added : englishCustomerServiceAddedModelCounts) {
            resultMap.computeIfAbsent(added.getCustomerServiceId(), id -> new CustomerServiceAddedOustModelCountInfo())
                    .setCustomerServiceId(added.getCustomerServiceId());
            resultMap.get(added.getCustomerServiceId()).setAddedCount(added.getAddedCount());
        }

        // 合并 oustCount
        for (CustomerServiceAddedOustModelCountInfo oust : englishCustomerServiceOustModelCounts) {
            resultMap.computeIfAbsent(oust.getCustomerServiceId(), id -> new CustomerServiceAddedOustModelCountInfo())
                    .setCustomerServiceId(oust.getCustomerServiceId());
            resultMap.get(oust.getCustomerServiceId()).setOustCount(oust.getOustCount());
        }

        return new ArrayList<>(resultMap.values());
    }

    /**
     * 通过模特状态查询模特
     */
    @Override
    public List<Model> selectListByStatus(Integer status) {
        return baseMapper.selectListByStatus(status);
    }

    /**
     * 查询模特信息（预选模特对象）
     */
    @Override
    public List<AddPreselectModelListVO> selectModelInfoOfPreselection(ModelListDTO modelListDTO) {
        List<ModelVO> modelVOS = baseMapper.selectModelListByCondition(modelListDTO);
        if (CollUtil.isEmpty(modelVOS)) {
            return Collections.emptyList();
        }
        List<AddPreselectModelListVO> addPreselectModelListVOS = BeanUtil.copyToList(modelVOS, AddPreselectModelListVO.class);

        //  获取关联标签
        List<Long> modelId = addPreselectModelListVOS.stream().map(AddPreselectModelListVO::getId).collect(Collectors.toList());
        Map<Long, List<ModelTagVO>> modelTagVOS = modelTagService.getModelTagVOMap(modelId);

        List<ModelPerson> modelPeople = modelPersonService.selectListByModelId(modelId);
        List<Long> personId = modelPeople.stream().map(ModelPerson::getUserId).collect(Collectors.toList());
        Map<Long, UserVO> userMap = remoteService.getUserMap(SysUserListDTO.builder().userId(personId).build());
        Map<Long, List<ModelPerson>> modelPeopleMap = modelPeople.stream().collect(Collectors.groupingBy(ModelPerson::getModelId));

        // 遍历处理每个模特信息，设置关联的资源数据
        for (AddPreselectModelListVO addPreselectModelListVO : addPreselectModelListVOS) {
            // 查询关联人员
            List<ModelPerson> people = modelPeopleMap.get(addPreselectModelListVO.getId());
            if (CollUtil.isNotEmpty(people)) {
                for (ModelPerson person : people) {
                    UserVO userVo = userMap.get(person.getUserId());
                    if (userVo != null) {
                        addPreselectModelListVO.getPersons().add(userVo);
                    }
                }
            }
            addPreselectModelListVO.echo(modelTagVOS.get(addPreselectModelListVO.getId()));

        }
        return addPreselectModelListVOS;
    }

    @Override
    public List<ModelBlackListUserVO> queryModelBlackListUserVO(Long modelId) {
        Assert.notNull(modelId, "模特ID不能为空");
        return baseMapper.queryModelBlackListUserVO(modelId);
    }

    @Override
    public ModelVO selectModelByModelLoginAccount(String account) {
        ModelAccount modelAccount = modelAccountService.lambdaQuery()
                .eq(ModelAccount::getLoginAccount, account)
                .one();
        Assert.notNull(modelAccount, "模特账号数据不存在！");
        ModelVO modelVO = this.selectModelById(modelAccount.getModelId());
        Assert.notNull(modelVO, "模特数据不存在！");
        if (!ModelStatusEnum.CANCEL.getCode().equals(modelVO.getStatus())) {
            loginForAccount(account);
        }
        return modelVO;
    }

    /**
     * 修改排序
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSort(ModelSortDTO modelSortDTO) {
        baseMapper.updateSort(modelSortDTO);
        modelChangeRecordService.saveModelChangeRecord(ModelChangeRecordDTO.builder()
                .modelId(modelSortDTO.getId())
                .operateType(ModelChangeTypeEnum.CHANGE_SORT.getCode())
                .operateDetail(String.format("修改模特排序值为%s", modelSortDTO.getSort()))
                .build());
    }

    /**
     * 置顶模特数量统计
     */
    @Override
    public ModelTopCountVO topCount() {
        Long count = baseMapper.getTopCount(null);
        Integer surplusCount = modelTopMax - Convert.toInt(count);
        return ModelTopCountVO.builder().topCount(Convert.toInt(count)).surplusCount(surplusCount >= 0 ? surplusCount : 0).build();
    }

    /**
     * 查询模特信息变更记录
     */
    @Override
    public List<ModelChangeRecordVO> modelChangeRecord(Long modelId) {
        return modelChangeRecordService.listByModelId(modelId);
    }

    /**
     * 查询模特信息列表（无需登录）
     */
    @Override
    public List<ModelListSimpleVO> referenceList(BusinessAccountCollectModelDTO dto) {
        int nonNullFieldCount = FieldUtils.getNonNullFieldCount(dto);
        if (nonNullFieldCount > 2) {
            return Collections.emptyList();
        }
        //  获取有逾期未反馈素材和无法接单的模特
        dto.setCannotModel(remoteService.checkModelOverdueVideo(new ArrayList<>()));
        return baseMapper.referenceList(dto);
    }

    /**
     * 查询模特简单信息（用于订单列表模特数据）
     */
    @Override
    public List<ModelOrderSimpleVO> queryModelSimpleList(ModelListDTO modelListDTO) {

        List<ModelVO> modelVOS = baseMapper.selectModelListByCondition(modelListDTO);
        if (CollUtil.isEmpty(modelVOS)) {
            return Collections.emptyList();
        }
        if (StatusTypeEnum.YES.getCode().equals(modelListDTO.getIncludeFamily())) {
            List<Long> familyIds = modelVOS.stream().map(ModelVO::getFamilyId).filter(ObjectUtil::isNotNull).distinct().collect(Collectors.toList());
            modelListDTO.setId(null);
            modelListDTO.setFamilyIds(familyIds);
            if (CollUtil.isNotEmpty(familyIds)) {
                modelVOS = baseMapper.selectModelListByCondition(modelListDTO);
            }
        }
        return BeanUtil.copyToList(modelVOS, ModelOrderSimpleVO.class);
    }

    /**
     * 运营端-编辑订单-更换模特列表
     */
    @Override
    public List<ModelVO> editOrderChangeModelList(EditOrderChangeModelListDTO dto) {
        //  获取有逾期未反馈素材和无法接单的模特
        List<Long> cannotModel = remoteService.checkModelOverdueVideo(new ArrayList<>());
        dto.setCannotModel(cannotModel);
        if (ObjectUtil.isNotNull(dto.getFilterBlackListBizUserId())) {
            List<UserBlackModelVO> userBlackModelVOS = userModelBlacklistService.userAllBlackModelList(dto.getFilterBlackListBizUserId());
            if (CollUtil.isNotEmpty(userBlackModelVOS)) {
                cannotModel.addAll(userBlackModelVOS.stream().map(UserBlackModelVO::getModelId).collect(Collectors.toList()));
            }
        }
        PageUtils.startPage();
        List<ModelVO> modelVOS = baseMapper.editOrderChangeModelList(dto);

        if (CollUtil.isEmpty(modelVOS)) {
            return Collections.emptyList();
        }

        //  获取模特待拍数、已完成订单数、超时订单
        List<ModelOrderVO> modelOrderCount = Optional.ofNullable(remoteService.getModelOrderCount(new ArrayList<>())).orElse(Collections.emptyList());
        Map<Long, ModelOrderVO> modelOrderVOMap = getModelOrderMap(modelOrderCount);
        assembleModelListV2(modelVOS, modelOrderVOMap);
        return modelVOS;
    }

    /**
     * 模特行程时间开始与结束 更新模特状态
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateModelTravelStatus() {
        String beginOfDay = DateUtil.format(DateUtil.beginOfDay(DateUtil.date()), DatePattern.NORM_DATETIME_PATTERN);
        List<Model> modelStartTravelList = baseMapper.modelStartTravelList(beginOfDay);
        List<Model> modelEndTravelList = baseMapper.modelEndTravelList(beginOfDay);

        List<ModelChangeRecordDTO> modelChangeRecordDTOS = new ArrayList<>();
        List<OutPreselectModelDTO> outPreselectModelDTOS = new ArrayList<>();

        if (CollUtil.isNotEmpty(modelStartTravelList)) {
            List<Long> modelIds = modelStartTravelList.stream().map(Model::getId).collect(Collectors.toList());
            List<ModelTravel> modelTravels = modelTravelService.selectModelTravelByModelIds(modelIds);
            Map<Long, ModelTravel> travelMap = modelTravels.stream().collect(Collectors.toMap(ModelTravel::getModelId, item -> item));

            List<ModelChangeRecord> modelChangeRecords = new ArrayList<>();
            DateTime date = DateUtil.date();
            for (Model item : modelStartTravelList) {
                OutPreselectModelDTO outPreselectModelDTO = new OutPreselectModelDTO();
                outPreselectModelDTO.setModelId(item.getId());
                outPreselectModelDTO.setModelStatus(ModelStatusEnum.JOURNEY.getCode());
                outPreselectModelDTOS.add(outPreselectModelDTO);

                item.setStatus(ModelStatusEnum.JOURNEY.getCode());
                if (ObjectUtil.isNotNull(item.getTopTime())) {
                    ModelChangeRecord modelChangeRecord = new ModelChangeRecord();
                    modelChangeRecord.setModelId(item.getId());
                    modelChangeRecord.setOperateTime(DateUtil.offsetSecond(date, 3));
                    modelChangeRecord.setOperateObject(EventExecuteObjectEnum.SYSTEM.getCode());
                    modelChangeRecord.setOperateUserName(EventExecuteObjectEnum.SYSTEM.getLabel());
                    modelChangeRecord.setOperateType(ModelChangeTypeEnum.CHANGE_TOP.getCode());
                    modelChangeRecord.setOperateDetail("取消置顶模特");
                    modelChangeRecords.add(modelChangeRecord);
                }
                item.setTopTime(null);
                ModelTravel modelTravel = travelMap.get(item.getId());
                if (modelTravel == null) {
                    continue;
                }
                ModelChangeRecordDTO build = ModelChangeRecordDTO.builder()
                        .modelId(item.getId())
                        .operateType(ModelChangeTypeEnum.CHANGE_STATUS.getCode())
                        .operateDetail(ModelStatusEnum.getLabelByCode(item.getStatus()) + StrUtil.COLON + DateUtil.format(modelTravel.getStartTime(), DatePattern.NORM_DATE_PATTERN) + StrUtil.DASHED + DateUtil.format(modelTravel.getEndTime(), DatePattern.NORM_DATE_PATTERN))
                        .operateExplain("行程开始")
                        .build();
                modelChangeRecordDTOS.add(build);
                modelOustSnapShootService.removeMonthModelSnapShoot(item.getId());
            }
            modelChangeRecordService.saveBatch(modelChangeRecords);
        }

        for (Model item : modelEndTravelList) {
            //行程恢复同步提示语清空
            item.setSyncMsg(null);
            item.setStatus(ModelStatusEnum.NORMAL.getCode());
            modelChangeRecordDTOS.add(ModelChangeRecordDTO.builder()
                    .modelId(item.getId())
                    .operateType(ModelChangeTypeEnum.CHANGE_STATUS.getCode())
                    .operateDetail(ModelStatusEnum.getLabelByCode(item.getStatus()))
                    .operateExplain("行程结束")
                    .build());
            modelOustSnapShootService.removeMonthModelSnapShoot(item.getId());
        }

        modelChangeRecordService.saveBatchModelChangeRecord(modelChangeRecordDTOS);
        modelStartTravelList.addAll(modelEndTravelList);
        updateOrderVideoBatchFieldNullToNull(modelStartTravelList);
        remoteService.outPreselectModel(outPreselectModelDTOS);
    }

    /**
     * 模糊查询模特信息列表（模特名称、模特账号）
     */
    @Override
    public List<ModelInfoVO> queryLikeModelList(ModelListDTO modelListDTO) {
        List<ModelVO> modelVOS = baseMapper.queryLikeModelList(modelListDTO);
        if (CollUtil.isEmpty(modelVOS)) return new ArrayList<>();

        return BeanUtil.copyToList(modelVOS, ModelInfoVO.class);
    }

    /**
     * 模特列表-获取关联人员下拉框（运营端）
     */
    @Override
    public List<UserVO> modelPersonsSelect(String keyword) {
        return modelPersonService.modelPersonsSelect(keyword);
    }

    /**
     * 组装导出信息
     */
    @Override
    public List<ModelExportDTO> getExportModel(List<ModelVO> list) {
        List<Long> modelIds = list.stream().map(ModelVO::getId).collect(Collectors.toList());
        final Map<Long, ModelTravel> modelTravelMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(modelIds)) {
            List<ModelTravel> modelTravels = modelTravelService.selectModelTravelByModelIds(modelIds);
            modelTravelMap.putAll(modelTravels.stream().collect(Collectors.toMap(ModelTravel::getModelId, mt -> mt, (e, r) -> e)));
        }
        Map<String, List<SysDictData>> sysDictMap = Arrays.stream(SysDictTypeEnum.values()).collect(Collectors.toMap(SysDictTypeEnum::getValue, dictType -> remoteSysDictTypeService.queryDictType(dictType.getValue(), SecurityConstants.INNER)));
        return list.stream().map(modelVO -> {
            ModelExportDTO modelExportDTO = BeanUtil.copyProperties(modelVO, ModelExportDTO.class);
            modelExportDTO.setAfterSaleRate(modelVO.getAfterSaleRate().multiply(BigDecimal.valueOf(100)));
            modelExportDTO.setOvertimeRate(modelVO.getOvertimeRate().multiply(BigDecimal.valueOf(100)));
            if (CollUtil.isNotEmpty(modelVO.getPersons())) {
                StringBuilder personsExportStr = new StringBuilder();
                for (UserVO person : modelVO.getPersons()) {
                    personsExportStr.append(person.getName()).append(StrUtil.COMMA).append(person.getPhonenumber()).append(";");
                }
                personsExportStr.deleteCharAt(personsExportStr.length() - 1);
                modelExportDTO.setPersonsExportStr(personsExportStr.toString());
            }
            if (CollUtil.isNotEmpty(modelVO.getAmazonVideo())) {
                modelExportDTO.setAmazonVideoCount(modelVO.getAmazonVideo().size());
            }
            if (CollUtil.isNotEmpty(modelVO.getTiktokVideo())) {
                modelExportDTO.setTikTokVideoCount(modelVO.getTiktokVideo().size());
            }

            if (CollUtil.isNotEmpty(modelVO.getSpecialtyCategory())) {
                StringBuilder specialtyCategoryExportStr = new StringBuilder();
                for (ModelTagVO tagVO : modelVO.getSpecialtyCategory()) {
                    specialtyCategoryExportStr.append(tagVO.getName()).append(StrUtil.COMMA);
                }
                specialtyCategoryExportStr.deleteCharAt(specialtyCategoryExportStr.length() - 1);
                modelExportDTO.setSpecialtyCategoryExportStr(specialtyCategoryExportStr.toString());
            }

            if (CollUtil.isNotEmpty(modelVO.getTags())) {
                StringBuilder tagsExportStr = new StringBuilder();
                for (ModelTagVO tagVO : modelVO.getTags()) {
                    tagsExportStr.append(tagVO.getName()).append(StrUtil.COMMA);
                }
                tagsExportStr.deleteCharAt(tagsExportStr.length() - 1);
                modelExportDTO.setTagsExportStr(tagsExportStr.toString());
            }
            if (modelVO.getNation().equals(NationEnum.UK.getCode())) {
                modelExportDTO.setModelAddress(String.format(
                                "Recipient Name:%s\n" +
                                        "House Number and Street Name:%s\n" +
                                        "City or Town:%s\n" +
                                        "Locality:%s\n" +
                                        "Postal Code:%s\n" +
                                        "number:%s\n",
                                StringUtils.isEmpty(modelVO.getRecipient()) ? "-" : modelVO.getRecipient(),
                                StringUtils.isEmpty(modelVO.getDetailAddress()) ? "-" : modelVO.getDetailAddress(),
                                StringUtils.isEmpty(modelVO.getCity()) ? "-" : modelVO.getCity(),
                                StringUtils.isEmpty(modelVO.getState()) ? "-" : modelVO.getState(),
                                StringUtils.isEmpty(modelVO.getZipcode()) ? "-" : modelVO.getZipcode(),
                                StringUtils.isEmpty(modelVO.getPhone()) ? "-" : modelVO.getPhone()
                        )
                );
            }
            if (modelVO.getNation().equals(NationEnum.CANADA.getCode())) {
                modelExportDTO.setModelAddress(String.format(
                                "Name:%s\n" +
                                        "Street Address:%s\n" +
                                        "City:%s\n" +
                                        "Province:%s\n" +
                                        "Postal Code:%s\n" +
                                        "number:%s\n",
                                StringUtils.isEmpty(modelVO.getRecipient()) ? "-" : modelVO.getRecipient(),
                                StringUtils.isEmpty(modelVO.getDetailAddress()) ? "-" : modelVO.getDetailAddress(),
                                StringUtils.isEmpty(modelVO.getCity()) ? "-" : modelVO.getCity(),
                                StringUtils.isEmpty(modelVO.getState()) ? "-" : modelVO.getState(),
                                StringUtils.isEmpty(modelVO.getZipcode()) ? "-" : modelVO.getZipcode(),
                                StringUtils.isEmpty(modelVO.getPhone()) ? "-" : modelVO.getPhone()
                        )
                );
            }
            if (modelVO.getNation().equals(NationEnum.GERMANY.getCode())) {
                modelExportDTO.setModelAddress(String.format(
                                "Empfängername:%s\n" +
                                        "Straße und Hausnummer:%s\n" +
                                        "Ort:%s\n" +
                                        "Bundesländer:%s\n" +
                                        "Postleitzahl:%s\n" +
                                        "number:%s",
                                StringUtils.isEmpty(modelVO.getRecipient()) ? "-" : modelVO.getRecipient(),
                                StringUtils.isEmpty(modelVO.getDetailAddress()) ? "-" : modelVO.getDetailAddress(),
                                StringUtils.isEmpty(modelVO.getCity()) ? "-" : modelVO.getCity(),
                                StringUtils.isEmpty(modelVO.getState()) ? "-" : modelVO.getState(),
                                StringUtils.isEmpty(modelVO.getZipcode()) ? "-" : modelVO.getZipcode(),
                                StringUtils.isEmpty(modelVO.getPhone()) ? "-" : modelVO.getPhone()
                        )
                );
            }
            if (modelVO.getNation().equals(NationEnum.FRANCE.getCode())) {
                modelExportDTO.setModelAddress(String.format(
                                "Nom:%s\n" +
                                        "Street Address:%s\n" +
                                        "Ville:%s\n" +
                                        "Département:%s\n" +
                                        "Code postal:%s\n" +
                                        "number:%s",
                                StringUtils.isEmpty(modelVO.getRecipient()) ? "-" : modelVO.getRecipient(),
                                StringUtils.isEmpty(modelVO.getDetailAddress()) ? "-" : modelVO.getDetailAddress(),
                                StringUtils.isEmpty(modelVO.getCity()) ? "-" : modelVO.getCity(),
                                StringUtils.isEmpty(modelVO.getState()) ? "-" : modelVO.getState(),
                                StringUtils.isEmpty(modelVO.getZipcode()) ? "-" : modelVO.getZipcode(),
                                StringUtils.isEmpty(modelVO.getPhone()) ? "-" : modelVO.getPhone()
                        )
                );
            }
            if (modelVO.getNation().equals(NationEnum.IT.getCode())) {
                modelExportDTO.setModelAddress(String.format(
                                "Nome del destinatario:%s\n" +
                                        "Via e numero civico:%s\n" +
                                        "Provincia:%s\n" +
                                        "Città:%s\n" +
                                        "CAP:%s\n" +
                                        "number:%s",
                                StringUtils.isEmpty(modelVO.getRecipient()) ? "-" : modelVO.getRecipient(),
                                StringUtils.isEmpty(modelVO.getDetailAddress()) ? "-" : modelVO.getDetailAddress(),
                                StringUtils.isEmpty(modelVO.getState()) ? "-" : modelVO.getState(),
                                StringUtils.isEmpty(modelVO.getCity()) ? "-" : modelVO.getCity(),
                                StringUtils.isEmpty(modelVO.getZipcode()) ? "-" : modelVO.getZipcode(),
                                StringUtils.isEmpty(modelVO.getPhone()) ? "-" : modelVO.getPhone()
                        )
                );
            }
            if (modelVO.getNation().equals(NationEnum.ES.getCode())) {
                modelExportDTO.setModelAddress(String.format(
                                "Nombre del destinatario:%s\n" +
                                        "Dirección (calle y número):%s\n" +
                                        "Provincias:%s\n" +
                                        "Ciudad:%s\n" +
                                        "Código postal:%s\n" +
                                        "number:%s",
                                StringUtils.isEmpty(modelVO.getRecipient()) ? "-" : modelVO.getRecipient(),
                                StringUtils.isEmpty(modelVO.getDetailAddress()) ? "-" : modelVO.getDetailAddress(),
                                StringUtils.isEmpty(modelVO.getState()) ? "-" : modelVO.getState(),
                                StringUtils.isEmpty(modelVO.getCity()) ? "-" : modelVO.getCity(),
                                StringUtils.isEmpty(modelVO.getZipcode()) ? "-" : modelVO.getZipcode(),
                                StringUtils.isEmpty(modelVO.getPhone()) ? "-" : modelVO.getPhone()
                        )
                );
            }
            if (modelVO.getNation().equals(NationEnum.USA.getCode())) {
                modelExportDTO.setModelAddress(String.format(
                                "Name:%s\n" +
                                        "Street Address:%s\n" +
                                        "City:%s\n" +
                                        "State:%s\n" +
                                        "Postal code:%s\n" +
                                        "number:%s",
                                StringUtils.isEmpty(modelVO.getRecipient()) ? "-" : modelVO.getRecipient(),
                                StringUtils.isEmpty(modelVO.getDetailAddress()) ? "-" : modelVO.getDetailAddress(),
                                StringUtils.isEmpty(modelVO.getCity()) ? "-" : modelVO.getCity(),
                                StringUtils.isEmpty(modelVO.getState()) ? "-" : modelVO.getState(),
                                StringUtils.isEmpty(modelVO.getZipcode()) ? "-" : modelVO.getZipcode(),
                                StringUtils.isEmpty(modelVO.getPhone()) ? "-" : modelVO.getPhone()
                        )
                );
            }

            if (modelVO.getStatus().equals(ModelStatusEnum.PAUSE.getCode())) {
                modelExportDTO.setCancelCooperationSubType(modelVO.getStatusExplain());
            } else if (modelVO.getStatus().equals(ModelStatusEnum.JOURNEY.getCode())) {
                ModelTravel modelTravel = modelTravelMap.get(modelVO.getId());
                if (modelTravel != null && null != modelTravel.getStartTime() && null != modelTravel.getEndTime() && StrUtil.isNotBlank(modelVO.getStatusExplain())) {
                    modelExportDTO.setCancelCooperationSubType(String.format("%s至%s  %s", DateFormatUtils.format(modelTravel.getStartTime(), "yyyy-MM-dd HH:mm:ss"), DateFormatUtils.format(modelTravel.getEndTime(), "yyyy-MM-dd HH:mm:ss"), modelVO.getStatusExplain()));
                }
            } else {
                if (StrUtil.isNotBlank(modelVO.getCancelCooperationSubType())) {
                    List<String> splitSubType = Arrays.asList(modelVO.getCancelCooperationSubType().split(","));
                    if (CollUtil.isNotEmpty(splitSubType)) {
                        List<SysDictData> dictDataList = sysDictMap.get(modelVO.getCancelCooperationType() == 0 ? SysDictTypeEnum.BIZ_WE_CANCEL_TYPE.getValue() : SysDictTypeEnum.BIZ_MODEL_CANCEL_TYPE.getValue());
                        modelExportDTO.setCancelCooperationSubType(dictDataList.stream().filter(ddl -> splitSubType.contains(ddl.getDictValue())).map(SysDictData::getDictLabel).collect(Collectors.joining(",")));
                    }
                }
            }

            modelExportDTO.setCreateUserName(Optional.ofNullable(modelVO.getCreateUser()).orElse(new UserVO()).getName());
            modelExportDTO.setCreateUserPhone(Optional.ofNullable(modelVO.getCreateUser()).orElse(new UserVO()).getPhonenumber());

            return modelExportDTO;
        }).collect(Collectors.toList());
    }

    @Override
    public ModelVO selectModelByModelAccount(String account) {
        ModelAccount modelAccount = modelAccountService.lambdaQuery()
                .eq(ModelAccount::getAccount, account)
                .one();
        Assert.notNull(modelAccount, "模特账号数据不存在！");
        ModelVO modelVO = this.selectModelById(modelAccount.getModelId());
        Assert.notNull(modelVO, "模特数据不存在！");
        if (!ModelStatusEnum.CANCEL.getCode().equals(modelVO.getStatus())) {
            loginForAccount(account);
        }
        return modelVO;
    }

    @Override
    public void loginForAccount(String account) {
        modelAccountService.updateLoginTime(account);
    }

    /**
     * 添加预选模特列表
     */
    @Override
    public PreselectModelListResultVO addPreselectModelList(AddPreselectModelListDTO addPreselectModelListDTO) {
        // 用于标记是否限制为优质模特
        boolean isLimitedToQualityModel = false;
        List<Long> modelIds = new ArrayList<>();
        if (!SecurityUtils.isAdmin(SecurityUtils.getUserId())) {
            //  获取当前运营关联的模特
            List<ModelPerson> modelPersonList = modelPersonService.selectListByUserIds(List.of(SecurityUtils.getUserId()));
            if (CollUtil.isEmpty(modelPersonList)) {
                return new PreselectModelListResultVO().setList(Collections.emptyList()).setIsLimitedToQualityModel(isLimitedToQualityModel);
            }

            modelIds = modelPersonList.stream().map(ModelPerson::getModelId).collect(Collectors.toList());
            addPreselectModelListDTO.setModelIds(modelIds);
        }

        //  查询有逾期未反馈素材和无法接单的模特
        List<Long> cannotModel = remoteService.checkModelOverdueVideo(modelIds);
        addPreselectModelListDTO.setCannotModel(cannotModel);

        //  获取模特待拍数、已完成订单数、超时订单、可携带订单数
        List<ModelOrderVO> modelOrderCount = remoteService.getModelOrderCount(new ArrayList<>());
        Map<Long, ModelOrderVO> modelOrderVOMap = getModelOrderMap(modelOrderCount);

        Map<Long, Long> waitsMap = new HashMap<>();
        for (Map.Entry<Long, ModelOrderVO> entry : modelOrderVOMap.entrySet()) {
            waitsMap.put(entry.getKey(), entry.getValue().getWaits());
        }
        addPreselectModelListDTO.setWaitsMap(waitsMap);

        if (ObjectUtil.isNotNull(addPreselectModelListDTO.getCarry())) {
            List<Long> mainCarryModel = modelOrderCount.stream().filter(item -> ObjectUtil.isNotNull(item.getCarryCount()) && item.getCarryCount() > 0).map(ModelOrderVO::getModelId).collect(Collectors.toList());
            addPreselectModelListDTO.setMainCarryModel(mainCarryModel);
        }
        if (ObjectUtil.isNotNull(addPreselectModelListDTO.getIsGund())) {
            Date orderFirstMatchTime = remoteService.getOrderFirstMatchTime(addPreselectModelListDTO.getVideoId());
            if (orderFirstMatchTime != null) {
                long daysElapsed = DateUtil.betweenDay(orderFirstMatchTime, new Date(), true);
                if (daysElapsed <= BusinessConstants.COMMON_PRODUCT_QUALITY_MODEL_DAYS_THRESHOLD) {
                    if (StatusTypeEnum.YES.getCode().equals(addPreselectModelListDTO.getIsGund())) {
                        // 通品：3天内（第0、1、2天）仅可选择优质模特
                        if (daysElapsed < BusinessConstants.COMMON_PRODUCT_QUALITY_MODEL_DAYS_THRESHOLD) {
                            addPreselectModelListDTO.setCooperation(ModelCooperationEnum.QUALITY.getCode());
                            isLimitedToQualityModel = true;
                        }
                    } else {
                        // 非通品：1天内（当天，第0天）仅可选择优质模特
                        if (daysElapsed < BusinessConstants.NON_COMMON_PRODUCT_QUALITY_MODEL_DAYS_THRESHOLD) {
                            addPreselectModelListDTO.setCooperation(ModelCooperationEnum.QUALITY.getCode());
                            isLimitedToQualityModel = true;
                        }
                    }
                }
            }
        }

        //  查询该匹配单下非淘汰的模特
        if (ObjectUtil.isNotNull(addPreselectModelListDTO.getMatchId())) {
            if (Boolean.TRUE.equals(addPreselectModelListDTO.getIsDistribution())) {
                Set<Long> preselectModelIds = remoteService.selectPreselectModelIdsByMatchId(addPreselectModelListDTO.getMatchId());
                addPreselectModelListDTO.setNormalPreselectModelIds(preselectModelIds);
            } else {
                Set<Long> normalPreselectModelIds = remoteService.selectNormalPreselectModelByMatchId(addPreselectModelListDTO.getMatchId());
                addPreselectModelListDTO.setNormalPreselectModelIds(normalPreselectModelIds);
            }
        }

        PageUtils.startPage();
        List<AddPreselectModelListVO> list = baseMapper.addPreselectModelList(addPreselectModelListDTO);
        if (CollUtil.isEmpty(list)) {
            return new PreselectModelListResultVO().setList(list).setIsLimitedToQualityModel(isLimitedToQualityModel);
        }

        //  获取关联标签
        List<Long> modelId = list.stream().map(AddPreselectModelListVO::getId).collect(Collectors.toList());
        Map<Long, List<ModelTagVO>> modelTagVOS = modelTagService.getModelTagVOMap(modelId);

        List<Long> userBlackModelIds = new ArrayList<>();
        if (ObjectUtil.isNotNull(addPreselectModelListDTO.getFilterBlackListBizUserId())) {
            List<UserBlackModelVO> userBlackModelVOS = userModelBlacklistService.userBlackModelList(addPreselectModelListDTO.getFilterBlackListBizUserId());
            if (CollUtil.isNotEmpty(userBlackModelVOS)) {
                userBlackModelIds = userBlackModelVOS.stream().map(UserBlackModelVO::getModelId).collect(Collectors.toList());
            }
        }

        //  获取被商家驳回的模特ID
        Set<Long> rejectModelIds = remoteService.selectRejectModelIdByVideoId(addPreselectModelListDTO.getVideoId());

        // 遍历处理每个模特信息，设置关联的资源数据
        for (AddPreselectModelListVO addPreselectModelListVO : list) {
            addPreselectModelListVO.echo(modelTagVOS.get(addPreselectModelListVO.getId()));
            ModelOrderVO modelOrderVO = modelOrderVOMap.getOrDefault(addPreselectModelListVO.getId(), new ModelOrderVO());
            addPreselectModelListVO.setWaits(modelOrderVO.getWaits());
            addPreselectModelListVO.setToBeConfirm(modelOrderVO.getToBeConfirm());
            addPreselectModelListVO.setCan(addPreselectModelListVO.getAcceptability() - (modelOrderVO.getWaits() + modelOrderVO.getUnconfirmed()));
            addPreselectModelListVO.setCarryCount(modelOrderVO.getCarryCount());

            addPreselectModelListVO.setIsBlack(userBlackModelIds.contains(addPreselectModelListVO.getId()));
            addPreselectModelListVO.setIsReject(rejectModelIds.contains(addPreselectModelListVO.getId()));
        }
        return new PreselectModelListResultVO().setList(list).setIsLimitedToQualityModel(isLimitedToQualityModel);
    }

    /**
     * 查询模特关联运营
     */
    @Override
    public List<ModelPerson> queryModelPerson(Collection<Long> modelIds) {
        return modelPersonService.selectListByModelId(modelIds);
    }

    /**
     * 模特列表下拉框
     */
    @Override
    public List<ModelVO> selectList(ModelListDTO modelListDTO) {
        return baseMapper.selectModelListByCondition(modelListDTO);
    }

    /**
     * 查询不可接单的模特（内部请求）
     */
    @Override
    public List<Model> queryCannotAcceptList(CannotAcceptModelDTO dto) {
        //订单状态不可接单模特
        List<Model> modelList = baseMapper.queryCannotAcceptList(dto.getModelId());

        List<Model> resultModels = new ArrayList<>();
        if (CollUtil.isNotEmpty(modelList)) {
            resultModels.addAll(modelList);
        }
        if (ObjectUtil.isNotNull(dto.getBizUserId())) {
            //黑名单不可接单模特
            List<Model> blacklistModel = baseMapper.queryBlacklistModel(dto);
            if (CollUtil.isNotEmpty(blacklistModel)) {
                resultModels.addAll(blacklistModel);
            }
        }
        return resultModels;
    }

    /**
     * 查询模特信息列表（内部请求）
     */
    @Override
    public List<ModelInfoVO> innerList(ModelListDTO modelListDTO) {
        List<ModelVO> modelVOS = baseMapper.selectModelListByCondition(modelListDTO);
        if (CollUtil.isEmpty(modelVOS)) {
            return Collections.emptyList();
        }

        List<ModelInfoVO> modelInfoVOList = BeanUtil.copyToList(modelVOS, ModelInfoVO.class);
        List<Long> modelId = modelVOS.stream().map(ModelVO::getId).filter(Objects::nonNull).collect(Collectors.toList());
        List<ModelPerson> modelPeople = modelPersonService.selectListByModelId(modelId);
        Map<Long, List<ModelPerson>> modelPeopleMap = modelPeople.stream().collect(Collectors.groupingBy(ModelPerson::getModelId));

        List<Long> personId = modelPeople.stream().map(ModelPerson::getUserId).collect(Collectors.toList());
        SysUserListDTO dto = new SysUserListDTO();
        dto.setUserId(personId);
        Map<Long, UserVO> userMap = remoteService.getUserMap(dto);

        Map<Long, List<Long>> userModelBlacklistMap = new HashMap<>();

        if (ObjectUtil.isNotNull(modelListDTO) && ObjectUtil.isNotNull(modelListDTO.getNeedBlacklist())
                && StatusTypeEnum.YES.getCode().equals(modelListDTO.getNeedBlacklist())) {
            //加载拉黑数据列表
            List<UserModelBlacklist> userModelBlacklists = userModelBlacklistService.userBlackModelListByModelIds(modelId);
            if (CollUtil.isNotEmpty(userModelBlacklists)) {
                userModelBlacklistMap.putAll(userModelBlacklists.stream()
                        .collect(Collectors.groupingBy(UserModelBlacklist::getModelId,
                                Collectors.mapping(UserModelBlacklist::getBizUserId, Collectors.toList()))));
            }
        }

        for (ModelInfoVO modelInfoVO : modelInfoVOList) {
            if (CollUtil.isNotEmpty(userModelBlacklistMap)) {
                List<Long> bizUserIds = userModelBlacklistMap.getOrDefault(modelInfoVO.getId(), Collections.emptyList());
                modelInfoVO.setBlacklistUserIds(bizUserIds);
            }
            // 查询关联人员
            List<ModelPerson> people = modelPeopleMap.get(modelInfoVO.getId());
            if (CollUtil.isNotEmpty(people)) {
                for (ModelPerson person : people) {
                    UserVO userVo = userMap.get(person.getUserId());
                    if (userVo != null) {
                        modelInfoVO.getPersons().add(userVo);
                    }
                }
            }
        }
        return modelInfoVOList;
    }

    /**
     * 创建模特后台链接
     */
    @Override
    public String backgroundLink(Long id) {
        Assert.notNull(id, "模特Id不能为空");
        ModelAccount modelAccount = modelAccountService.getOneByModelId(id);
        if (StringUtils.isNull(modelAccount)) {
            throw new ServiceException("不存在对应模特数据");
        }
        return modelProperties.getBaseUrl() + modelAccount.getLoginAccount();
    }

    /**
     * 修改模特关联人员
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateRelevance(ModelRelevanceDTO modelRelevanceDTO) {
        //  删除原有
        modelPersonService.removeByModelId(modelRelevanceDTO.getModelIds());

        //  新增
        List<Long> modelIds = modelRelevanceDTO.getModelIds();
        List<Long> userIds = modelRelevanceDTO.getUserIds();

        List<ModelPerson> modelPeople = new ArrayList<>();

        for (Long modelId : modelIds) {
            for (Long userId : userIds) {
                ModelPerson modelPerson = new ModelPerson();
                modelPerson.setModelId(modelId);
                modelPerson.setUserId(userId);
                modelPerson.setUpdateBy(SecurityUtils.getUsername());
                modelPerson.setUpdateBy(SecurityUtils.getUsername());
                modelPeople.add(modelPerson);
            }
        }
        modelPersonService.saveBatch(modelPeople);
        remoteService.updateIssueId(UpdateIssueIdDTO.builder().modelIds(modelRelevanceDTO.getModelIds()).issueId(modelRelevanceDTO.getUserIds().get(0)).build());
    }

    /**
     * 模特关联人员
     */
    @Override
    public List<UserVO> relevance(Long id) {
        List<ModelPerson> modelPeople = modelPersonService.selectListByModelId(Collections.singletonList(id));

        List<UserVO> userVOS = new ArrayList<>();
        if (CollUtil.isEmpty(modelPeople)) {
            return userVOS;
        }

        List<Long> userIds = modelPeople.stream().map(ModelPerson::getUserId).collect(Collectors.toList());
        //  获取用户map
        SysUserListDTO dto = new SysUserListDTO();
        dto.setUserId(userIds);
        Map<Long, UserVO> userMap = remoteService.getUserMap(dto);

        for (ModelPerson person : modelPeople) {
            UserVO userVO = userMap.get(person.getUserId());
            if (userVO != null) {
                userVOS.add(userVO);
            }
        }

        return userVOS;
    }

    /**
     * 删除模特关联表
     *
     * @param id 模特id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeModelRelevance(Long id) {
        //  删除标签表
        modelTagService.removeByModelId(Collections.singletonList(id));

        //  删除关联人员表
        modelPersonService.removeByModelId(Collections.singletonList(id));

        //  删除模特关联案例视频表
        modelResourceService.removeByModelId(id);
    }

    /**
     * 查询模特信息
     *
     * @param id 模特信息主键
     * @return 模特信息
     */
    @Override
    public ModelVO selectModelById(Long id) {
        ModelVO modelVo = baseMapper.selectModelAndRelevanceById(id);
        if (modelVo == null) {
            return null;
        }

        //  获取模特待拍数、已完成订单数、超时订单
        List<ModelOrderVO> modelOrderCount = Optional.ofNullable(remoteService.getModelOrderCount(CollUtil.toList(id))).orElse(Collections.emptyList());
        Map<Long, ModelOrderVO> modelOrderVOMap = getModelOrderMap(modelOrderCount);
        assembleModelListV2(Collections.singletonList(modelVo), modelOrderVOMap);

        return modelVo;
    }


    /**
     * 查询模特信息列表
     *
     * @param modelListDTO 模特信息
     * @return 模特信息
     */
    @Override
    public List<ModelVO> selectModelListByCondition(ModelListDTO modelListDTO) {
        Map<Long, ModelOrderVO> modelOrderVOMap = wrapperCondition(modelListDTO);
        if (CollUtil.isNotEmpty(modelListDTO.getModelFamilyRelationships()) && CollUtil.isEmpty(modelListDTO.getFamilyIds())) {
            return new ArrayList<>();
        }
        PageUtils.startPageNoDefault();
        List<ModelVO> modelVOS = baseMapper.selectModelListByCondition(modelListDTO);
        long modelTotal = new PageInfo(modelVOS).getTotal();
        if (modelTotal < 50 && modelTotal > 0 && StatusTypeEnum.YES.getCode().equals(modelListDTO.getIncludeFamily())) {
            List<Long> familyIds = modelVOS.stream().map(ModelVO::getFamilyId).filter(item -> ObjectUtil.isNotNull(item)).distinct().collect(Collectors.toList());
            if (CollUtil.isNotEmpty(familyIds)) {
                PageUtils.startPageNoDefault();
                modelListDTO.setIncludeFamilyIds(familyIds);
                modelVOS = baseMapper.selectModelListByCondition(modelListDTO);
            }
        }


        if (CollUtil.isEmpty(modelVOS)) {
            return new ArrayList<>();
        }

        assembleModelListV2(modelVOS, modelOrderVOMap);
        return modelVOS;
    }

    @Override
    public List<ModelVO> selectModelFamilyListByCondition(ModelListDTO modelListDTO) {
        Map<Long, ModelOrderVO> modelOrderVOMap = wrapperCondition(modelListDTO);
        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("m.is_initiator", OrderByDto.DIRECTION.DESC);
        orderByDto.setField("m.join_family_time", OrderByDto.DIRECTION.DESC);
        orderByDto.setField("m.id", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);
        List<ModelVO> modelVOS = baseMapper.selectModelFamilyListByCondition(modelListDTO);
        if (CollUtil.isEmpty(modelVOS)) {
            return new ArrayList<>();
        }
        assembleModelListV2(modelVOS, modelOrderVOMap);
        return modelVOS;
    }

    @Override
    public List<ModelFamilyVO> selectModelFamilyByFamilyId(Long familyId) {
        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("m.is_initiator", OrderByDto.DIRECTION.DESC);
        orderByDto.setField("m.join_family_time", OrderByDto.DIRECTION.DESC);
        orderByDto.setField("m.id", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);
        return baseMapper.selectModelFamilyByFamilyId(familyId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addFamilyModel(ModelFamilyInsertDTO dto) {
        Assert.isFalse(dto.getFamilyId().equals(dto.getModelId()), "模特不能添加本人为家庭成员~");
        Assert.isFalse(ModelFamilyRelationshipTypeEnum.INITIATOR.getCode().equals(dto.getModelFamilyRelationship()), "关系不能选择发起人~");
        //判断添加模特是否有效
        Model model = baseMapper.selectById(dto.getModelId());
        Assert.notNull(model, "添加模特不能为空~");
        Assert.isTrue(StatusTypeEnum.NO.getCode().equals(model.getIsFamilyModel()), "添加模特已经是其他家庭的模特~");

        if (baseMapper.getModelFamilyMemberCount(dto.getFamilyId()) > 0) {
            //存在模特家庭-直接加入家庭
            modelJoinFamily(dto);
        } else {
            Model initiatorModel = baseMapper.selectById(dto.getFamilyId());
            Assert.notNull(initiatorModel, "主要联系人不能为空~");
            Assert.isTrue(StatusTypeEnum.NO.getCode().equals(initiatorModel.getIsFamilyModel()), "该模特已加入家庭成员，无法再创建家庭~");
            //不存在模特家庭-初始化模特家庭
            initModelFamily(dto);
        }
        modelChangeRecordService.saveModelChangeRecord(ModelChangeRecordDTO.builder()
                .modelId(dto.getFamilyId())
                .operateType(ModelChangeTypeEnum.UPDATE_FAMILY_MODEL.getCode())
                .operateDetail("添加家庭成员：" + model.getName())
                .build());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteFamilyModel(ModelFamilyDeleteDTO dto) {
        Model model = baseMapper.selectById(dto.getModelId());
        Assert.notNull(model, "添加模特不能为空~");
        Assert.isTrue(StatusTypeEnum.NO.getCode().equals(model.getIsInitiator()), "不能删除发起人");
        Assert.notNull(model.getFamilyId(), "该模特已离开家庭~");
        Assert.isTrue(model.getFamilyId().equals(dto.getFamilyId()), "该模特不属于本家庭");
        Long modelFamilyMemberCount = baseMapper.getModelFamilyMemberCount(dto.getFamilyId());
        if (modelFamilyMemberCount == 2) {
            //清空家庭
            baseMapper.clearFamilyMember(Arrays.asList(dto.getFamilyId(), dto.getModelId()));
        } else if (modelFamilyMemberCount > 2) {
            //家庭成员离开
            baseMapper.clearFamilyMember(Arrays.asList(dto.getModelId()));
        } else {
            throw new ServiceException("已不存在家庭，请刷新重试");
        }
        modelChangeRecordService.saveModelChangeRecord(ModelChangeRecordDTO.builder()
                .modelId(dto.getFamilyId())
                .operateType(ModelChangeTypeEnum.UPDATE_FAMILY_MODEL.getCode())
                .operateDetail("删除家庭成员：" + model.getName())
                .build());
    }

    /**
     * 初始化模特家庭
     *
     * @param dto
     */
    public void initModelFamily(ModelFamilyInsertDTO dto) {
        List<Model> modelList = new ArrayList<>();
        Model initiatorModel = new Model();
        initiatorModel.setId(dto.getFamilyId());
        initiatorModel.setFamilyId(dto.getFamilyId());
        initiatorModel.setIsInitiator(StatusTypeEnum.YES.getCode());
        initiatorModel.setModelFamilyRelationship(ModelFamilyRelationshipTypeEnum.INITIATOR.getCode());
        initiatorModel.setJoinFamilyTime(new Date());
        initiatorModel.setIsFamilyModel(StatusTypeEnum.YES.getCode());

        Model familyMemberModel = new Model();
        familyMemberModel.setId(dto.getModelId());
        familyMemberModel.setFamilyId(dto.getFamilyId());
        familyMemberModel.setModelFamilyRelationship(dto.getModelFamilyRelationship());
        familyMemberModel.setJoinFamilyTime(new Date());
        familyMemberModel.setIsFamilyModel(StatusTypeEnum.YES.getCode());
        modelList.add(initiatorModel);
        modelList.add(familyMemberModel);
        this.updateBatchById(modelList);
    }

    /**
     * 模特加入家庭
     * * @param dto
     */
    public void modelJoinFamily(ModelFamilyInsertDTO dto) {
        Model familyMemberModel = new Model();
        familyMemberModel.setId(dto.getModelId());
        familyMemberModel.setFamilyId(dto.getFamilyId());
        familyMemberModel.setModelFamilyRelationship(dto.getModelFamilyRelationship());
        familyMemberModel.setJoinFamilyTime(new Date());
        familyMemberModel.setIsFamilyModel(StatusTypeEnum.YES.getCode());
        this.updateById(familyMemberModel);
    }

    private Map<Long, ModelOrderVO> wrapperCondition(ModelListDTO modelListDTO) {
        //  获取模特待拍数、已完成订单数、超时订单
        List<ModelOrderVO> modelOrderCount = remoteService.getModelOrderCount(new ArrayList<>());
        Map<Long, ModelOrderVO> modelOrderVOMap = getModelOrderMap(modelOrderCount);

        Map<Long, Long> waitsMap = new HashMap<>();
        Map<Long, Long> toBeConfirmMap = new HashMap<>();
        for (Map.Entry<Long, ModelOrderVO> entry : modelOrderVOMap.entrySet()) {
            waitsMap.put(entry.getKey(), entry.getValue().getWaits());
            toBeConfirmMap.put(entry.getKey(), entry.getValue().getToBeConfirm());
        }
        modelListDTO.setWaitsMap(waitsMap);
        modelListDTO.setToBeConfirmMap(toBeConfirmMap);

        if (CollUtil.isNotEmpty(modelListDTO.getModelFamilyRelationships())) {
            List<Long> modelIdByRelationships = baseMapper.getModelIdByRelationships(modelListDTO.getModelFamilyRelationships());
            if (CollUtil.isNotEmpty(modelIdByRelationships)) {
                modelListDTO.setFamilyIds(modelIdByRelationships);
                modelListDTO.setIsInitiator(StatusTypeEnum.YES.getCode());
            }
        }

        return modelOrderVOMap;
    }

    @Override
    public List<ModelChangeVO> selectModelChangeList(ModelListDTO modelListDTO) {
        List<ModelVO> modelVOS = baseMapper.selectModelListByCondition(modelListDTO);
        if (CollUtil.isEmpty(modelVOS)) {
            return Collections.emptyList();
        }
        List<ModelChangeVO> modelChangeVOS = BeanUtil.copyToList(modelVOS, ModelChangeVO.class);

        List<ModelPerson> modelPeople = modelPersonService.selectListByModelId(modelListDTO.getId());
        //  获取关联标签
        Map<Long, List<ModelTagVO>> modelTagVOS = modelTagService.getModelTagVOMap(modelListDTO.getId());

        // 获取用户信息的任务
        CompletableFuture<Map<Long, UserVO>> userFuture = CompletableFuture.supplyAsync(() -> {
            List<Long> userId = new ArrayList<>();
            List<Long> createByIds = modelVOS.stream().map(ModelVO::getCreateBy).collect(Collectors.toList());
            userId.addAll(createByIds);

            List<Long> personId = modelPeople.stream().map(ModelPerson::getUserId).collect(Collectors.toList());
            userId.addAll(personId);

            SysUserListDTO dto = new SysUserListDTO();
            dto.setUserId(userId);
            return remoteService.getUserMap(dto);
        }, asyncPoolTaskExecutor);

        // 获取ModelPerson数据的任务
        CompletableFuture<Map<Long, List<ModelPerson>>> modelPeopleFuture = CompletableFuture.supplyAsync(() ->
                        modelPeople.stream().collect(Collectors.groupingBy(ModelPerson::getModelId))
                , asyncPoolTaskExecutor);

        // 组装数据
        CompletableFuture<Void> allOf = CompletableFuture.allOf(userFuture, modelPeopleFuture);
        allOf.join();

        try {
            Map<Long, UserVO> userMap = userFuture.get();
            Map<Long, List<ModelPerson>> modelPeopleMap = modelPeopleFuture.get();

            for (ModelChangeVO modelChangeVO : modelChangeVOS) {
                // 查询关联人员
                List<ModelPerson> people = modelPeopleMap.get(modelChangeVO.getId());
                if (CollUtil.isNotEmpty(people)) {
                    for (ModelPerson person : people) {
                        UserVO userVo = userMap.get(person.getUserId());
                        if (userVo != null) modelChangeVO.getPersons().add(userVo);
                    }
                }
                modelChangeVO.echo(modelTagVOS.get(modelChangeVO.getId()));
            }
        } catch (Exception e) {
            log.error(ERROR_REPORTED_ASSEMBLING_MODEL_LIST, e);
            throw new ServiceException(ERROR_REPORTED_ASSEMBLING_MODEL_LIST);
        }
        return modelChangeVOS;
    }

    /**
     * 新增模特信息
     *
     * @param modelDTO 模特信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertModel(ModelDTO modelDTO) {
        Assert.isFalse(baseMapper.checkModelNameExist(modelDTO.getId(), modelDTO.getName()), "模特名称已存在！");
        Model model = init(modelDTO);
        model.setCreateBy(SecurityUtils.getUserId());
        //  添加模特
        baseMapper.insert(model);

        modelAccountService.createModelAccount(model.getId());
        //  添加模特关联表
        SpringUtils.getAopProxy(this).saveModelRelevance(modelDTO, model.getId());

        modelChangeRecordService.saveModelChangeRecord(ModelChangeRecordDTO.builder()
                .modelId(model.getId())
                .operateType(ModelChangeTypeEnum.INSERT.getCode())
                .build());

        modelAddedSnapShootService.saveModelAddedSnapShoot(model);
    }

    /**
     * 修改模特信息
     *
     * @param modelDTO 模特信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateModel(ModelDTO modelDTO) {
        Assert.isFalse(baseMapper.checkModelNameExist(modelDTO.getId(), modelDTO.getName()), "模特名称已存在！");
        Model modelTable = baseMapper.selectById(modelDTO.getId());
        ModelDTO modelDTOTable = BeanUtil.copyProperties(modelTable, ModelDTO.class);
        Map<String, Object> stringObjectMap;
        Model model = init(modelDTO);
        try {
            if (CollUtil.isEmpty(modelDTO.getLifePhoto())) {
                modelDTO.setLifePhoto(null);
            }
            fillModelInfo(modelTable, modelDTOTable);
            stringObjectMap = ObjectUtils.compareAndReturnDifferencesKeyAnnotation(modelDTOTable, modelDTO);
            if (CollUtil.isNotEmpty(stringObjectMap) && stringObjectMap.containsKey("州") && !NationEnum.USA.getCode().equals(modelDTO.getNation())) {
                NationEnum nationEnum = NationEnum.getNationEnum(modelDTO.getNation());
                stringObjectMap.put(nationEnum.getStateName(), stringObjectMap.get("州"));
                stringObjectMap.remove("州");
            }
        } catch (IllegalAccessException e) {
            throw new ServiceException("获取模特差异错误！");
        }
        baseMapper.updateById(model);

        IModelService modelService = SpringUtils.getAopProxy(this);

        //  删除关联表
        modelService.removeModelRelevance(modelDTO.getId());

        //  新增关联表
        modelService.saveModelRelevance(modelDTO, model.getId());

        modelChangeRecordService.saveModelChangeRecord(ModelChangeRecordDTO.builder()
                .modelId(model.getId())
                .operateType(ModelChangeTypeEnum.UPDATE.getCode())
                .operateDetail(CollUtil.isNotEmpty(stringObjectMap) ? "变更：" + StrUtil.join(StrUtil.COMMA, stringObjectMap.keySet()) : null)
                .build());
        if (CollUtil.isEmpty(stringObjectMap)) {
            return;
        }
        List<String> temp = new ArrayList<>(stringObjectMap.keySet());
        List<String> address = List.of("州", "城市", "省", "局部区域", "门牌号和街道名称", "邮编", "收件人", "电话");
        for (String key : temp) {
            if (address.contains(key)) {
                remoteService.modelUpdateAddress(ModelUpdateAddressDTO.builder().modelId(modelTable.getId()).modelName(model.getName()).build());
                break;
            }
        }
        remoteService.updateIssueId(UpdateIssueIdDTO.builder().modelIds(List.of(modelDTO.getId())).issueId(modelDTO.getPersons().get(0)).build());

    }

    private void fillModelInfo(Model modelTable, ModelDTO modelDTOTable) {
        Map<Long, BizResource> livePicMap = new HashMap<>();
        Map<Long, List<ModelVideoResource>> longModelVideoResourceMap = getModelVideoResourceMapByModelIds(List.of(modelDTOTable.getId()));

        List<Long> livePicList = StringUtils.splitToLong(modelTable.getLivePic(), StrUtil.COMMA);
        livePicList.removeIf(Objects::isNull);
        if (CollUtil.isNotEmpty(livePicList)) {
            livePicMap = bizResourceService.getResourceMapByIds(livePicList);
        }
        //处理dto
        if (CollUtil.isNotEmpty(longModelVideoResourceMap)) {
            List<ModelVideoResource> modelVideoResources = longModelVideoResourceMap.get(modelTable.getId());
            //填充视频数据
            if (CollUtil.isNotEmpty(modelVideoResources)) {
                modelDTOTable.setAmazonVideo(BeanUtil.copyToList(modelVideoResources.stream().filter(item -> ModelVideoResourceTypeEnum.AMAZON_VIDEO.getCode().equals(item.getType())).collect(Collectors.toList()), ModelResourceDTO.class));
                modelDTOTable.setTiktokVideo(BeanUtil.copyToList(modelVideoResources.stream().filter(item -> ModelVideoResourceTypeEnum.TIKTOK_VIDEO.getCode().equals(item.getType())).collect(Collectors.toList()), ModelResourceDTO.class));
            }
        }
        if (CollUtil.isNotEmpty(livePicMap)) {
            List<String> collect = new ArrayList<>();
            for (Long resourceId : StringUtils.splitToLong(modelTable.getLivePic(), StrUtil.COMMA)) {
                collect.add(livePicMap.getOrDefault(resourceId, new BizResource()).getObjectKey());
            }
            if (CollUtil.isNotEmpty(collect)) {
                modelDTOTable.setLifePhoto(collect);
            }

        }

        List<ModelPerson> modelPeople = modelPersonService.selectListByModelId(Arrays.asList(modelTable.getId()));
        if (CollUtil.isNotEmpty(modelPeople)) {
            modelDTOTable.setPersons(modelPeople.stream().map(ModelPerson::getUserId).collect(Collectors.toList()));
        }

        Map<Long, List<ModelTagVO>> modelTagVOS = modelTagService.getModelTagVOMap(Arrays.asList(modelTable.getId()));
        modelDTOTable.echo(modelTagVOS.get(modelTable.getId()));
    }

    /**
     * 添加模特关联表
     *
     * @param modelDTO 新增修改模特入参
     * @param modelId  新增的模特id
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveModelRelevance(ModelDTO modelDTO, Long modelId) {
        modelDTO.init(modelId);

        List<ModelTag> modelTags = new ArrayList<>();
        //  擅长品类
        for (Long specialtyCategory : modelDTO.getSpecialtyCategory()) {
            ModelTag modelTag = new ModelTag();
            modelTag.setDictId(specialtyCategory);
            modelTag.setModelId(modelId);
            modelTags.add(modelTag);
        }
        //  模特标签
        for (ModelTagDTO tag : modelDTO.getTags()) {
            ModelTag modelTag = new ModelTag();
            modelTag.setDictId(tag.getId());
            modelTag.setDictName(tag.getName());
            modelTag.setModelId(modelId);
            modelTag.setDictCategoryId(ObjectUtil.isNull(tag.getId()) ? ModelTagEnum.TAG.getCode() : null);
            modelTags.add(modelTag);
        }

        //  写入标签表
        modelTagService.saveBatch(modelTags);

        //  关联的对接人
        List<ModelPerson> modelPeople = modelDTO.getPersons().stream().map(item -> {
            ModelPerson modelPerson = new ModelPerson();
            modelPerson.setUserId(item);
            modelPerson.setModelId(modelId);
            modelPerson.setUpdateBy(SecurityUtils.getUsername());
            modelPerson.setUpdateBy(SecurityUtils.getUsername());
            return modelPerson;
        }).collect(Collectors.toList());
        modelPersonService.saveBatch(modelPeople);

        modelResourceService.insertResource(modelDTO.getAmazonVideo());
    }

    /**
     * 更新模特状态
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateModelStatus(ModelUpdateStatusDTO dto) {
        Model model = baseMapper.selectById(dto.getId());
        Assert.notNull(model, "模特不存在！");

        // 记录原始状态，用于判断是否删除旅行记录
        Integer originalStatus = model.getStatus();

        DateTime now = DateUtil.date();
        LoginUserInfoVO loginUser = SecurityUtils.getLoginUserInfoVo();
        String syncMsg = getSyncMsg(dto, model);

        List<ModelChangeRecord> changeRecords = new ArrayList<>();
        List<OutPreselectModelDTO> outPreselectModelDTOS = new ArrayList<>();
        List<ModelTravel> travelRecordsToInsert = new ArrayList<>();
        List<Long> modelIdsToDeleteTravel = new ArrayList<>();
        List<Model> modelsToUpdate = new ArrayList<>(); // 用于收集所有需要更新的Model对象

        if (Boolean.TRUE.equals(dto.getIsSync())) {
            handleFamilySync(dto, model, now, loginUser, syncMsg,
                    changeRecords, outPreselectModelDTOS, travelRecordsToInsert, modelIdsToDeleteTravel, modelsToUpdate);
        } else {
            handleSingleModelStatus(dto, model, originalStatus, now, loginUser, syncMsg,
                    changeRecords, outPreselectModelDTOS, travelRecordsToInsert, modelIdsToDeleteTravel, modelsToUpdate);
        }

        // 统一执行数据库操作
        if (CollUtil.isNotEmpty(modelsToUpdate)) {
            // 根据实际情况选择批量更新或逐个更新，MyBatis-Plus的updateById默认是按ID更新所有非空字段
            // 如果DTO中某些字段未传递但希望保持不变，需要更精细的Wrapper更新
            // 这里假设baseMapper.updateById可以处理，或者需要自定义批量更新方法
            // baseMapper.updateBatchById(modelsToUpdate); // 如果是Mybatis-Plus的Service，可以直接用updateBatchById
            updateOrderVideoBatchFieldNullToNull(modelsToUpdate);
//            modelsToUpdate.forEach(item -> baseMapper.updateById(item)); // 逐个更新，或者实现批量更新方法
        }

        if (CollUtil.isNotEmpty(travelRecordsToInsert)) {
            modelTravelService.insertBatchModelTravel(travelRecordsToInsert);
        }
        if (CollUtil.isNotEmpty(modelIdsToDeleteTravel)) {
            modelTravelService.removeByModelIds(modelIdsToDeleteTravel);
        }

        modelChangeRecordService.saveBatch(changeRecords);
        remoteService.outPreselectModel(outPreselectModelDTOS);

        // 锁逻辑
        if (ModelStatusEnum.CANCEL.getCode().equals(dto.getStatus())) {
            redisService.getLock(CacheConstants.MODEL_LOGIN_LOCK_KEY + dto.getId(), CacheConstants.EXPIRATION * CacheConstants.MINUTE_SECOND);
        } else {
            redisService.releaseLock(CacheConstants.MODEL_LOGIN_LOCK_KEY + dto.getId());
        }
    }

    /**
     * 获取同步信息
     */
    private String getSyncMsg(ModelUpdateStatusDTO dto, Model model) {
        return ModelStatusEnum.JOURNEY.getCode().equals(dto.getStatus())
                ? String.format("当前行程源于主要联系人[%s]同步", model.getName())
                : String.format("当前状态源于主要联系人[%s]同步", model.getName());
    }

    /**
     * 处理家庭成员同步逻辑
     */
    private void handleFamilySync(ModelUpdateStatusDTO dto, Model mainModel, DateTime now, LoginUserInfoVO loginUser, String syncMsg,
                                  List<ModelChangeRecord> changeRecords, List<OutPreselectModelDTO> outPreselectModelDTOS,
                                  List<ModelTravel> travelRecordsToInsert, List<Long> modelIdsToDeleteTravel,
                                  List<Model> modelsToUpdate) {
        List<Model> familyModels = baseMapper.selectListByFamilyId(mainModel.getFamilyId());
        if (CollUtil.isEmpty(familyModels)) {
            return;
        }

        for (Model item : familyModels) {
            Integer stateBeforeChange = item.getStatus();

            boolean isTargetPauseOrCancel = ModelStatusEnum.PAUSE.getCode().equals(dto.getStatus()) || ModelStatusEnum.CANCEL.getCode().equals(dto.getStatus());
            boolean isCurrentPauseOrCancel = ModelStatusEnum.PAUSE.getCode().equals(item.getStatus()) || ModelStatusEnum.CANCEL.getCode().equals(item.getStatus());

            if (isCurrentPauseOrCancel && isTargetPauseOrCancel) {
                continue;
            }

            // 应用统一的模型状态更新逻辑
            applyModelStatusUpdateLogic(item, dto, now, syncMsg, loginUser,
                    changeRecords, outPreselectModelDTOS, travelRecordsToInsert,
                    item.getStatus(),
                    modelIdsToDeleteTravel);

            modelsToUpdate.add(item);

            // 构建变更记录，只针对当前更新的模特
            String operateDetail = buildOperateDetail(dto);

            // 备注信息特殊处理：
            String finalOperateExplain;
            if (mainModel.getId().equals(item.getId())) {
                finalOperateExplain = dto.getStatusExplain();
            } else {
                // 如果是家庭成员，拼接同步信息。确保两部分都非null，然后拼接并trim。
                finalOperateExplain = String.format("%s %s",
                                StrUtil.isNotBlank(dto.getStatusExplain()) ? dto.getStatusExplain() : "",
                                StrUtil.isNotBlank(syncMsg) ? syncMsg : "")
                        .trim(); // 确保拼接后的字符串两端没有多余空格
            }

            ModelChangeRecord modelChangeRecord = buildChangeRecord(item.getId(), ModelChangeTypeEnum.CHANGE_STATUS.getCode(), operateDetail,
                    finalOperateExplain, now, loginUser, stateBeforeChange);
            changeRecords.add(modelChangeRecord);
        }
        updateOrderVideoBatchFieldNullToNull(modelsToUpdate);
    }


    /**
     * 处理单个模特状态变更逻辑
     */
    private void handleSingleModelStatus(ModelUpdateStatusDTO dto, Model model, Integer originalStatus, DateTime now, LoginUserInfoVO loginUser, String syncMsg,
                                         List<ModelChangeRecord> changeRecords, List<OutPreselectModelDTO> outPreselectModelDTOS,
                                         List<ModelTravel> travelRecordsToInsert, List<Long> modelIdsToDeleteTravel,
                                         List<Model> modelsToUpdate) {
        Integer stateBeforeChange = model.getStatus(); // 记录当前状态，用于changeRecord

        // 应用统一的模型状态更新逻辑
        applyModelStatusUpdateLogic(model, dto, now, syncMsg, loginUser,
                changeRecords, outPreselectModelDTOS, travelRecordsToInsert,
                originalStatus, // 传入主模型的原始状态
                modelIdsToDeleteTravel);

        modelsToUpdate.add(model); // 将修改后的model加入待更新列表

        // 构建变更记录
        String operateDetail = buildOperateDetail(dto);
        ModelChangeRecord modelChangeRecord = buildChangeRecord(dto.getId(), ModelChangeTypeEnum.CHANGE_STATUS.getCode(), operateDetail,
                dto.getStatusExplain(), now, loginUser, stateBeforeChange); // 使用传入model的初始状态
        changeRecords.add(modelChangeRecord);
    }

    /**
     * 统一处理模型状态更新的核心逻辑
     * 该方法会修改传入的 modelToUpdate 对象的状态和相关字段
     */
    private void applyModelStatusUpdateLogic(Model modelToUpdate, ModelUpdateStatusDTO dto, DateTime now, String syncMsg, LoginUserInfoVO loginUser,
                                             List<ModelChangeRecord> changeRecords, List<OutPreselectModelDTO> outPreselectModelDTOS,
                                             List<ModelTravel> travelRecordsToInsert, Integer originalStatusOfModel, List<Long> modelIdsToDeleteTravel) {

        boolean isJourneyStatusTarget = ModelStatusEnum.JOURNEY.getCode().equals(dto.getStatus());
        boolean isPauseOrCancelTarget = ModelStatusEnum.PAUSE.getCode().equals(dto.getStatus()) || ModelStatusEnum.CANCEL.getCode().equals(dto.getStatus());
        boolean wasJourneyStatus = ModelStatusEnum.JOURNEY.getCode().equals(originalStatusOfModel);

        modelToUpdate.setStatusTime(now);
        modelToUpdate.setSyncMsg(StrUtil.isNotBlank(syncMsg) ? syncMsg : null);

        // 如果原始状态是JOURNEY，且目标状态不是JOURNEY，则需要删除旅行记录
        if (wasJourneyStatus && !isJourneyStatusTarget) {
            modelIdsToDeleteTravel.add(modelToUpdate.getId());
        }

        if (isJourneyStatusTarget) {
            dto.setEndTime(DateUtil.parseDateTime(DateUtil.format(DateUtil.endOfDay(dto.getEndTime()), DatePattern.NORM_DATETIME_PATTERN)));

            ModelTravel modelTravel = new ModelTravel();
            modelTravel.setStartTime(dto.getStartTime());
            modelTravel.setEndTime(dto.getEndTime());
            modelTravel.setModelId(modelToUpdate.getId());
            travelRecordsToInsert.add(modelTravel); // 添加到待插入列表

            // 判断当前日期是否在行程日期内
            if (DateUtil.isIn(now, DateUtil.beginOfDay(dto.getStartTime()), DateUtil.beginOfDay(dto.getEndTime()))) {
                // 在行程日期内，设为行程状态
                modelToUpdate.setStatus(dto.getStatus());
                fillPauseOrJourneyFields(modelToUpdate, dto); // 填充暂停/行程相关字段
                cancelTopIfNeeded(modelToUpdate, now, changeRecords); // 取消置顶
                modelOustSnapShootService.removeMonthModelSnapShoot(modelToUpdate.getId());
            } else {
                // 不在行程日期内，设为正常状态
                modelToUpdate.setStatus(ModelStatusEnum.NORMAL.getCode());
                modelToUpdate.setCancelCooperationSubType(null); // 清除相关字段
                modelToUpdate.setCancelCooperationType(null);
                modelToUpdate.setStatusExplain(null);
                modelOustSnapShootService.removeMonthModelSnapShoot(modelToUpdate.getId());
            }
            addOutPreselectModelDTO(modelToUpdate, dto, outPreselectModelDTOS); // 行程状态也需要同步到预选
        } else if (isPauseOrCancelTarget) {
            // 目标状态是暂停或取消
            modelToUpdate.setStatus(dto.getStatus());
            fillPauseOrJourneyFields(modelToUpdate, dto); // 填充暂停/行程相关字段
            cancelTopIfNeeded(modelToUpdate, now, changeRecords); // 取消置顶
            modelOustSnapShootService.saveOrUpdateMonthModelSnapShoot(modelToUpdate); // 保存或更新月度快照
            addOutPreselectModelDTO(modelToUpdate, dto, outPreselectModelDTOS);
        } else {
            // 目标状态是其他（如NORMAL）
            modelToUpdate.setStatus(dto.getStatus());
            modelToUpdate.setCancelCooperationSubType(null); // 清除相关字段
            modelToUpdate.setCancelCooperationType(null);
            modelToUpdate.setStatusExplain(null);
            cancelTopIfNeeded(modelToUpdate, now, changeRecords); // 取消置顶
            modelOustSnapShootService.removeMonthModelSnapShoot(modelToUpdate.getId()); // 移除月度快照
        }
        // 注意：这里的modelToUpdate是引用，其属性修改会影响到调用方的list中的Model对象
    }

    /**
     * 构建操作详情
     */
    private String buildOperateDetail(ModelUpdateStatusDTO dto) {
        String operateDetail = ModelStatusEnum.getLabelByCode(dto.getStatus());
        if (ModelStatusEnum.JOURNEY.getCode().equals(dto.getStatus())) {
            operateDetail += StrUtil.COLON + DateUtil.format(dto.getStartTime(), DatePattern.NORM_DATE_PATTERN) + StrUtil.DASHED + DateUtil.format(dto.getEndTime(), DatePattern.NORM_DATE_PATTERN);
        }
        return operateDetail;
    }

    /**
     * 填充暂停/行程相关字段
     */
    private void fillPauseOrJourneyFields(Model model, ModelUpdateStatusDTO dto) {
        model.setCancelCooperationSubType(dto.getCancelCooperationSubType());
        model.setCancelCooperationType(dto.getCancelCooperationType());
        model.setStatusExplain(StrUtil.isNotBlank(dto.getStatusExplain()) ? dto.getStatusExplain() : null);
        // model.setStatus(dto.getStatus()); // 状态已在 applyModelStatusUpdateLogic 中设置
    }

    /**
     * 取消置顶并记录
     */
    private void cancelTopIfNeeded(Model model, DateTime now, List<ModelChangeRecord> changeRecords) {
        if (ObjectUtil.isNotNull(model.getTopTime())) {
            ModelChangeRecord modelChangeRecord = new ModelChangeRecord();
            modelChangeRecord.setModelId(model.getId());
            // 为了保证记录的顺序，操作时间可以稍微错开，或者由记录服务统一管理
            modelChangeRecord.setOperateTime(DateUtil.offsetSecond(now, 3));
            modelChangeRecord.setOperateObject(EventExecuteObjectEnum.SYSTEM.getCode());
            modelChangeRecord.setOperateUserName(EventExecuteObjectEnum.SYSTEM.getLabel());
            modelChangeRecord.setOperateType(ModelChangeTypeEnum.CHANGE_TOP.getCode());
            modelChangeRecord.setOperateDetail("取消置顶模特");
            // stateBeforeChange 此时是置顶前的状态，可以从model.getStatus()获取
            modelChangeRecord.setStateBeforeChange(model.getStatus());
            changeRecords.add(modelChangeRecord);
            model.setTopTime(null); // 真正取消置顶
        }
    }

    /**
     * 添加预选模特DTO
     */
    private void addOutPreselectModelDTO(Model model, ModelUpdateStatusDTO dto, List<OutPreselectModelDTO> outPreselectModelDTOS) {
        OutPreselectModelDTO outPreselectModelDTO = new OutPreselectModelDTO();
        outPreselectModelDTO.setModelId(model.getId());
        // 注意：这里的modelStatus应使用model的最终状态，而不是dto的原始状态
        // 因为在applyModelStatusUpdateLogic中，JOURNEY状态可能被修正为NORMAL
        outPreselectModelDTO.setModelStatus(model.getStatus());
        if (ModelStatusEnum.PAUSE.getCode().equals(model.getStatus())) {
            outPreselectModelDTO.setRemark(OrderConstant.ORDER_PRESELECT_MODEL_PAUSE_REMARK);
        } else if (ModelStatusEnum.CANCEL.getCode().equals(model.getStatus())) {
            outPreselectModelDTO.setRemark(OrderConstant.ORDER_PRESELECT_MODEL_CANCEL_REMARK);
        } else if (ModelStatusEnum.JOURNEY.getCode().equals(model.getStatus())) {
            outPreselectModelDTO.setRemark(OrderConstant.ORDER_PRESELECT_MODEL_ON_THE_ROAD);
        }
        outPreselectModelDTOS.add(outPreselectModelDTO);
    }

    /**
     * 构建变更记录
     */
    private ModelChangeRecord buildChangeRecord(Long modelId, Integer operateType, String operateDetail, String operateExplain, DateTime operateTime, LoginUserInfoVO loginUser, Integer stateBeforeChange) {
        ModelChangeRecord modelChangeRecord = new ModelChangeRecord();
        modelChangeRecord.setModelId(modelId);
        modelChangeRecord.setOperateType(operateType);
        modelChangeRecord.setOperateDetail(operateDetail);
        modelChangeRecord.setOperateExplain(StrUtil.nullToEmpty(operateExplain));
        modelChangeRecord.setOperateTime(operateTime);
        modelChangeRecord.setOperateObject(loginUser.getUserType());
        modelChangeRecord.setOperateUserId(loginUser.getUserId());
        modelChangeRecord.setOperateUserName(EventExecuteObjectEnum.SYSTEM.getCode().equals(loginUser.getUserType()) ? "系统" : loginUser.getName());
        modelChangeRecord.setStateBeforeChange(stateBeforeChange);
        return modelChangeRecord;
    }

    /**
     * 置顶模特
     */
    @Override
    public void top(Long id) {
        Model model = baseMapper.selectById(id);
        Assert.notNull(model, "模特不存在");
        Assert.isTrue(ModelStatusEnum.NORMAL.getCode().equals(model.getStatus()), "只允许置顶正常合作状态下的模特");
        Long count = baseMapper.getTopCount(id);
        if (count >= modelTopMax) {
            throw new ServiceException("超出置顶限制，最多允许置顶55个模特");
        }
        modelChangeRecordService.saveModelChangeRecord(ModelChangeRecordDTO.builder()
                .modelId(id)
                .operateType(ModelChangeTypeEnum.CHANGE_TOP.getCode())
                .operateDetail(model.getTopTime() != null ? "取消置顶模特" : "置顶模特")
                .build());
        baseMapper.top(id);
    }

    @Override
    public Map<Long, Integer> getFamilyCountMap() {
        List<ModelFamilyCountVO> familyCount = baseMapper.getFamilyCount();
        if (CollUtil.isEmpty(familyCount)) {
            return Collections.emptyMap();
        }
        return familyCount.stream().collect(Collectors.toMap(ModelFamilyCountVO::getFamilyId, ModelFamilyCountVO::getCount));
    }

    /**
     * 写入数据库前的初始化对象
     *
     * @param modelDTO 新增编辑模特入参对象
     */
    public Model init(ModelDTO modelDTO) {
        Model model = BeanUtil.copyProperties(modelDTO, Model.class);
        //  平台
        StringBuilder platformSB = new StringBuilder();
        for (String platform : StrUtil.split(modelDTO.getPlatform(), StrUtil.COMMA)) {
            platformSB.append(PlatformEnum.getLabel(Convert.toInt(platform))).append(StrUtil.COMMA);
        }
        platformSB.deleteCharAt(platformSB.length() - 1);
        model.setPlatformDict(platformSB.toString());

        //  模特类型
        StringBuilder typeSB = new StringBuilder();
        for (String type : StrUtil.split(modelDTO.getType().toString(), StrUtil.COMMA)) {
            typeSB.append(ModelTypeEnum.getLabel(Convert.toInt(type))).append(StrUtil.COMMA);
        }
        typeSB.deleteCharAt(typeSB.length() - 1);
        model.setTypeDict(typeSB.toString());
        if (modelDTO.getCooperationScore().compareTo(ModelCooperationEnum.QUALITY.getScore()) >= 0) {
            model.setCooperation(ModelCooperationEnum.QUALITY.getCode());
            modelDTO.setCooperation(ModelCooperationEnum.QUALITY.getCode());
        } else {
            model.setCooperation(ModelCooperationEnum.ORDINARY.getCode());
            modelDTO.setCooperation(ModelCooperationEnum.ORDINARY.getCode());
        }

        //  国家
        model.setNationDict(NationEnum.getLabel(modelDTO.getNation()));

        //  性别
        model.setSexDict(SexEnum.getLabel(modelDTO.getSex()));

        //  年龄层
        model.setAgeGroupDict(ModelAgeGroupEnum.getLabel(modelDTO.getAgeGroup()));

        //  生活场景照
        if (CollUtil.isNotEmpty(modelDTO.getLifePhoto())) {
            List<Long> lifePhotoIds = bizResourceService.saveBatchBizResourceReturnIds(modelDTO.getLifePhoto());
            model.setLivePic(StrUtil.join(StrUtil.COMMA, lifePhotoIds));
        } else {
            model.setLivePic(StrUtil.EMPTY);
        }

        //  计算年龄
        model.setAge(model.getBirthday() != null ? DateUtil.ageOfNow(model.getBirthday()) : null);

        model.setUpdateBy(SecurityUtils.getUserId());

        // 比较 案例视频/标签/品类 是否有更改 有更改设置各自更新时间
        DateTime dateTime = DateUtil.date();
        if (modelDTO.getOldAmazonVideo().size() != modelDTO.getAmazonVideo().size()) {
            model.setVideoLastUpdateTime(dateTime);
        } else {
            for (int i = 0; i < modelDTO.getOldAmazonVideo().size(); i++) {
                ModelVideoResource oldAmazonVideo = modelDTO.getOldAmazonVideo().get(i);
                ModelResourceDTO amazonVideo = modelDTO.getAmazonVideo().get(i);
                if (
                        ObjectUtil.notEqual(oldAmazonVideo.getName(), amazonVideo.getName())
                                || ObjectUtil.notEqual(oldAmazonVideo.getPicUri(), amazonVideo.getPicUri())
                                || ObjectUtil.notEqual(oldAmazonVideo.getVideoUrl(), amazonVideo.getVideoUrl())
                                || ObjectUtil.notEqual(oldAmazonVideo.getSort(), amazonVideo.getSort())
                ) {
                    model.setVideoLastUpdateTime(dateTime);
                    break;
                }
            }
        }

        if (modelDTO.getOldTiktokVideo().size() != modelDTO.getTiktokVideo().size()) {
            model.setVideoLastUpdateTime(dateTime);
        } else {
            for (int i = 0; i < modelDTO.getOldTiktokVideo().size(); i++) {
                ModelVideoResource oldTikTok = modelDTO.getOldTiktokVideo().get(i);
                ModelResourceDTO tikTok = modelDTO.getTiktokVideo().get(i);
                if (
                        ObjectUtil.notEqual(oldTikTok.getName(), tikTok.getName())
                                || ObjectUtil.notEqual(oldTikTok.getPicUri(), tikTok.getPicUri())
                                || ObjectUtil.notEqual(oldTikTok.getVideoUrl(), tikTok.getVideoUrl())
                                || ObjectUtil.notEqual(oldTikTok.getSort(), tikTok.getSort())
                ) {
                    model.setVideoLastUpdateTime(dateTime);
                    break;
                }
            }
        }

        if (modelDTO.getOldTags().size() != modelDTO.getTags().size()) {
            model.setTagLastUpdateTime(dateTime);
        } else {
            for (int i = 0; i < modelDTO.getOldTags().size(); i++) {
                ModelTagVO oldTags = modelDTO.getOldTags().get(i);
                ModelTagDTO tags = modelDTO.getTags().get(i);
                if (
                        (oldTags.getId() != null && ObjectUtil.notEqual(oldTags.getId(), tags.getId()))
                                || (CharSequenceUtil.isNotBlank(tags.getName()) && ObjectUtil.notEqual(oldTags.getName(), tags.getName()))
                ) {
                    model.setTagLastUpdateTime(dateTime);
                    break;
                }
            }
        }

        if (modelDTO.getOldSpecialtyCategory().size() != modelDTO.getSpecialtyCategory().size()) {
            model.setCategoryLastUpdateTime(dateTime);
        } else {
            for (int i = 0; i < modelDTO.getOldSpecialtyCategory().size(); i++) {
                ModelTagVO oldSpecialtyCategory = modelDTO.getOldSpecialtyCategory().get(i);
                Long specialtyCategory = modelDTO.getSpecialtyCategory().get(i);
                if (
                        ObjectUtil.notEqual(oldSpecialtyCategory.getId(), specialtyCategory)
                ) {
                    model.setCategoryLastUpdateTime(dateTime);
                    break;
                }
            }
        }

        return model;
    }

    @Override
    public String getMsg(String modelId) {
        return baseMapper.selectById(modelId).getSyncMsg();
    }

    /**
     * 模特关联资源
     *
     * @param modelIds 模特id
     * @return 模特相关资源
     */
    private Map<Long, List<ModelVideoResource>> getModelVideoResourceMapByModelIds(List<Long> modelIds) {
        List<ModelVideoResource> modelVideoResourceList = modelResourceService.selectListByModelIds(modelIds);

        return modelVideoResourceList.stream().collect(Collectors.groupingBy(ModelVideoResource::getModelId));
    }

    /**
     * 组装模特列表数据
     */
    private void assembleModelListV2(List<ModelVO> modelVOS, Map<Long, ModelOrderVO> modelOrderVOMap) {
        List<Long> modelId = modelVOS.stream().map(ModelVO::getId).collect(Collectors.toList());

        List<ModelPerson> modelPeople = modelPersonService.selectListByModelId(modelId);
        //  获取关联标签
        Map<Long, List<ModelTagVO>> modelTagVOS = modelTagService.getModelTagVOMap(modelId);

        Map<Long, Integer> familyCountMap = getFamilyCountMap();

        // 获取用户信息的任务
        CompletableFuture<Map<Long, UserVO>> userFuture = CompletableFuture.supplyAsync(() -> {
            List<Long> userId = new ArrayList<>();
            List<Long> createByIds = modelVOS.stream().map(ModelVO::getCreateBy).collect(Collectors.toList());
            userId.addAll(createByIds);

            List<Long> developerIds = modelVOS.stream().map(ModelVO::getDeveloperId).collect(Collectors.toList());
            userId.addAll(developerIds);

            List<Long> personId = modelPeople.stream().map(ModelPerson::getUserId).collect(Collectors.toList());
            userId.addAll(personId);

            SysUserListDTO dto = new SysUserListDTO();
            dto.setUserId(userId);
            return remoteService.getUserMap(dto);
        }, asyncPoolTaskExecutor);

        // 获取模特关联案例资源的任务
        CompletableFuture<Map<Long, List<ModelVideoResource>>> modelResourceFuture = CompletableFuture.supplyAsync(() -> getModelVideoResourceMapByModelIds(modelId), asyncPoolTaskExecutor);

        // 获取ModelPerson数据的任务
        CompletableFuture<Map<Long, List<ModelPerson>>> modelPeopleFuture = CompletableFuture.supplyAsync(() ->
                        modelPeople.stream().collect(Collectors.groupingBy(ModelPerson::getModelId))
                , asyncPoolTaskExecutor);

        //  获取模特待拍数、已完成订单数、超时订单
        // CompletableFuture<List<ModelOrderVO>> modelOrderFuture = CompletableFuture.supplyAsync(() ->
        //     remoteService.getModelOrderCount(modelId)
        // , asyncPoolTaskExecutor);

        //  获取模特被收藏数
        CompletableFuture<List<BusinessAccountCollectModel>> modelCollectCountFuture = CompletableFuture.supplyAsync(() ->
                        SpringUtils.getBean(IBusinessAccountCollectModelService.class).getModelCollectCount(modelId)
                , asyncPoolTaskExecutor);

        //  获取模特行程记录
        CompletableFuture<List<ModelTravel>> modelTravelFuture = CompletableFuture.supplyAsync(() ->
                        modelTravelService.selectModelTravelByModelIds(modelId)
                , asyncPoolTaskExecutor);

        //  获取模特关联biz_resource资源
        CompletableFuture<Map<Long, BizResource>> modelBizResourceMapFuture = CompletableFuture.supplyAsync(() -> {
            Set<String> livePicId = modelVOS.stream().map(ModelVO::getLivePicId).filter(Objects::nonNull).collect(Collectors.toSet());
            List<Long> resourceIds = StringUtils.splitToLong(livePicId, StrUtil.COMMA);
            return bizResourceService.getResourceMapByIds(resourceIds);
        }, asyncPoolTaskExecutor);

        // 组装数据
        CompletableFuture<Void> allOf = CompletableFuture.allOf(userFuture, modelResourceFuture,
                modelPeopleFuture,
                // modelOrderFuture,
                modelCollectCountFuture,
                modelTravelFuture, modelBizResourceMapFuture);
        allOf.join();

        try {
            Map<Long, UserVO> userMap = userFuture.get();
            Map<Long, List<ModelVideoResource>> resourceMap = modelResourceFuture.get();
            Map<Long, List<ModelPerson>> modelPeopleMap = modelPeopleFuture.get();
            // List<ModelOrderVO> modelOrderVOS = modelOrderFuture.get();
            List<BusinessAccountCollectModel> businessAccountCollectModels = modelCollectCountFuture.get();
            List<ModelTravel> modelTravels = modelTravelFuture.get();
            // Map<Long, ModelOrderVO> modelOrderVOMap = getModelOrderMap(modelOrderVOS);
            Map<Long, List<BusinessAccountCollectModel>> businessAccountCollectModelMap = getBusinessAccountCollectModelMap(businessAccountCollectModels);
            Map<Long, ModelTravel> modelTravelMap = getModelTravelMap(modelTravels);
            Map<Long, BizResource> bizResources = modelBizResourceMapFuture.get();

            for (ModelVO modelVO : modelVOS) {
                if (CollUtil.isNotEmpty(familyCountMap) && ObjectUtil.isNotNull(modelVO.getFamilyId())) {
                    modelVO.setFamilyNum(Optional.ofNullable(familyCountMap.get(modelVO.getFamilyId())).orElse(0));
                }
                for (Long resourceId : StringUtils.splitToLong(modelVO.getLivePicId(), StrUtil.COMMA)) {
                    modelVO.getLifePhoto().add(bizResources.getOrDefault(resourceId, new BizResource()).getObjectKey());
                }

                List<ModelVideoResource> modelVideoResources = resourceMap.get(modelVO.getId());
                //填充视频数据
                if (CollUtil.isNotEmpty(modelVideoResources)) {
                    modelVO.setAmazonVideo(modelVideoResources.stream().filter(item -> ModelVideoResourceTypeEnum.AMAZON_VIDEO.getCode().equals(item.getType())).sorted(Comparator.comparingInt(ModelVideoResource::getSort).reversed()).collect(Collectors.toList()));
                    modelVO.setTiktokVideo(modelVideoResources.stream().filter(item -> ModelVideoResourceTypeEnum.TIKTOK_VIDEO.getCode().equals(item.getType())).sorted(Comparator.comparingInt(ModelVideoResource::getSort).reversed()).collect(Collectors.toList()));
                }

                modelVO.getTiktokVideo().sort(Comparator.comparingInt(ModelVideoResource::getSort).reversed());

                // 查询关联人员
                List<ModelPerson> people = modelPeopleMap.get(modelVO.getId());
                if (CollUtil.isNotEmpty(people)) {
                    for (ModelPerson person : people) {
                        UserVO userVo = userMap.get(person.getUserId());
                        if (userVo != null) {
                            modelVO.getPersons().add(userVo);
                        }
                    }
                }
                modelVO.setCreateUser(userMap.get(modelVO.getCreateBy()));
                modelVO.setDeveloperUser(userMap.get(modelVO.getDeveloperId()));

                modelVO.echo(modelTagVOS.get(modelVO.getId()));

                ModelOrderVO modelOrderVO = modelOrderVOMap.getOrDefault(modelVO.getId(), new ModelOrderVO());
                modelVO.setCan(modelVO.getAcceptability() - (modelOrderVO.getWaits() + modelOrderVO.getUnconfirmed()));
                modelVO.setWaits(modelOrderVO.getWaits());
                modelVO.setToBeConfirm(modelOrderVO.getToBeConfirm());
                modelVO.setFinished(modelOrderVO.getFinished());

                List<BusinessAccountCollectModel> modelCollect = businessAccountCollectModelMap.getOrDefault(modelVO.getId(), new ArrayList<>());
                modelVO.setCollected((long) modelCollect.size());

                ModelTravel modelTravel = modelTravelMap.get(modelVO.getId());
                if (ObjectUtil.isNotEmpty(modelTravel)) {
                    modelVO.setTravel(BeanUtil.copyProperties(modelTravel, ModelTravelVO.class));
                    modelVO.setTravelEndTime(DateUtil.offsetDay(modelTravel.getEndTime(), 1));
                }
            }
        } catch (Exception e) {
            log.error(ERROR_REPORTED_ASSEMBLING_MODEL_LIST, e);
            throw new ServiceException(ERROR_REPORTED_ASSEMBLING_MODEL_LIST);
        }
    }

    private Map<Long, ModelTravel> getModelTravelMap(List<ModelTravel> modelTravels) {//	进行分组 值是对象 而不是集合
        return modelTravels.stream().collect(Collectors.toMap(ModelTravel::getModelId, p -> p));
    }

    private Map<Long, List<BusinessAccountCollectModel>> getBusinessAccountCollectModelMap(List<BusinessAccountCollectModel> businessAccountCollectModels) {
        return businessAccountCollectModels.stream().collect(Collectors.groupingBy(BusinessAccountCollectModel::getModelId));
    }

    private Map<Long, ModelOrderVO> getModelOrderMap(List<ModelOrderVO> modelOrderVOS) {
        return modelOrderVOS.stream().collect(Collectors.toMap(ModelOrderVO::getModelId, p -> p));
    }

    /**
     * 更新模特 若字段为null 更新为null
     * PS:请注意字段值
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateOrderVideoBatchFieldNullToNull(List<Model> models) {
        for (Model model : models) {
            baseMapper.updateModelInfoFieldNullToNull(model);
        }
    }
}
